import {useEffect, useState} from 'react';
import useGetRequest from '../useGetRequest';
import {COLLECTIONS_URL} from '../../constants/apiEndPoints';
import { AutomatedCollectionDetails } from '../../interfaces/collectionForm';

const useGetAutomatedCollectionDetails = (collectionId?: string) => {
    const [currentCollectionDetailsState, setCurrentCollectionDetailsState] =
        useState<AutomatedCollectionDetails | null>();
    const state = useGetRequest<AutomatedCollectionDetails>(
        `${COLLECTIONS_URL}/${collectionId}`
    );

    const {data: response} = state;

    useEffect(() => {
        if (response !== undefined) {
            setCurrentCollectionDetailsState(response);
        }
    }, [response]);

    return {...state, data: currentCollectionDetailsState};
};

export default useGetAutomatedCollectionDetails;