## Product Collection MFE

Treez Product Collection MFE

## Description

Management of Treez Product Collection

## How to Setup and Run Locally

1. Begin by installing packages using the `yarn` command.
1. _Skip this step if you have already installed mkcert on your computer_ Configure a certificate
   for localhost by referring to the instructions in this link:
   [Local Development Setup](https://im360us.atlassian.net/wiki/spaces/development/pages/2719187344/Local+Development+Setup).
1. Create a `.env.local` file and update the DOMAIN_URL to match the backend environment you intend
   to work in, such as api.{env}.treez.io.
1. Start the Micro Frontend (MFE) application by running the command
    1. `yarn start` or `yarn start:local:https`
1. Access the domain you wish to work on (e.g., app.{env}.treez.io). If you're not logged in, log in
   to be redirected to `app.{env}.treez.io/dashboard`.
1. Open the DevTools console and execute `localStorage.setItem('devtools', true);`, then refresh the
   page. This action will display the Import Map Overrides button on the bottom-right corner.
1. Click the button to access the Import Map Overrides user interface. Override product-collection
   by selecting `@treez/product-collection` and entering `3001` as the Override URL.

### Unit Tests

1. Unit tests are done via Jest (and React Testing Library for React components) and run with the
   command `yarn jest`.
1. Unit Test files should live in the component folder and be named in this format:
   Component.test.tsx where Component is the name of the folder of the component the test lives in.
1. File extensions should be .tsx for component (React Testing Library) tests and should be .ts if
   it is a regular non-component unit test as this will not require React Testing Library (ex: util
   function).

1. `test:coverage`

### End to End Tests

End to End tests (real browser tests) are done via Cypress and are run using the commands NOTE :
CYPRESS_BASE_URL to adapt to the updated environment

1. `cypress:open:{env}`
1. `cypress:run:{env}`

### Environment URLs

1. Dev (Sandbox): https://app.sandbox.treez.io/
1. Build: https://app.dev.treez.io/
1. Production: https://app.treez.io/

## Contribution Guidelines

### Code Formatting

Before pushing any code changes, make sure to format your code. Run the following command:

`yarn format` or `yarn format:fix`

This ensures that the code follows the project's formatting standards.

### Linting

Run the linting checks to identify and fix any potential issues in the code:

`yarn lint` or `yarn lint:fix`

Fix any reported issues before making a pull request.

### Branching Strategy

Follow our recommended branching strategy when creating new features or fixing issues:

1. Create a new branch for your feature or bug fix: git checkout -b your-feature-name or git
   checkout -b #issue-number-your-feature-name.
1. Commit your changes to this branch and push it to the remote repository.
1. Create a pull request against the appropriate base branch (usually main).

### Documentation

If your changes impact the project's documentation, please update the relevant documentation files
to reflect the changes you've made.

### Testing

Always include unit tests & integration tests to ensure the stability of your changes. Make sure
that all existing tests continue to pass.
