import { QueryObserverResult, useQuery } from 'react-query';
import { UseQueryOptions } from 'react-query/types/react/types';
import queryKeyStore from './queryKeyStore';
import { ProductSearchResponse } from '../interfaces/dto/product';

export interface ProductSearchQueryProps {
    search?: string;
    filters?: {
        status?: string[];
        brand?: string[];
        category?: string[];
        subCategory?: string[];
    };
    sort?: {
        key: string;
        isAscending: boolean;
    };
    paging?: {
        pageNumber: number;
        size: number;
    };
}

interface UseRequestResult {
    data: ProductSearchResponse[];
    totalRecords?: number;
    page?: number;
    pageSize?: number;
}

const useProductSearchQuery = (props: ProductSearchQueryProps, options?: UseQueryOptions) =>
    useQuery({
        ...queryKeyStore.productSearch.list(props),
        ...options,
    }) as QueryObserverResult<UseRequestResult, Error>;

export default useProductSearchQuery;
