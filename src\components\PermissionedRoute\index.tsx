import React from 'react';
import {PageLoader} from '@treez-inc/component-library';
import usePermissionsContext from '../../hooks/usePermissionsContext';
import ErrorPermissions from '../ErrorPermissions';
import CenteredContent from '../CenteredContent';

export interface PermissionedRouteProps {
    anyOf: string[];
    children: JSX.Element;
}

const PermissionedRoute: React.FC<PermissionedRouteProps> = ({anyOf, children}) => {
    const {permissions} = usePermissionsContext();

    // initial value, means we're still fetching the user's permissions
    if (permissions === null) {
        return (
            <CenteredContent testId="permissioned-route-loader">
                <PageLoader />
            </CenteredContent>
        );
    }

    const hasPermission = anyOf.some((neededPermission) => permissions[neededPermission] === true);

    if (!hasPermission) {
        return <ErrorPermissions />;
    }

    return children;
};

export default PermissionedRoute;
