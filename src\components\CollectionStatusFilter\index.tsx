import React, {useState, useEffect} from 'react';
import {CheckboxListMenuItem, DropdownChip, Menu} from '@treez-inc/component-library';
import {IFilterDropdown} from '../../interfaces/collectionFilter';
import CollectionStatus from '../../interfaces/collectionStatus.enum';
import useMenu from '../../hooks/filter/useMenu';

interface CollectionStatusCheckbox {
    key: CollectionStatus;
    value: string;
    label: string;
    checked?: boolean;
}

const getCheckboxes = (
    collectionStatuses: CollectionStatusCheckbox[],
    onChange: (checkboxKey: string | number, isChecked: boolean) => void
) =>
    collectionStatuses?.map((status: any) => ({
        key: status.key,
        label: status.label,
        checked: status.checked || false,
        onChange,
        value: status.value,
    }));

const CollectionStatusFilterComponent: React.FC<IFilterDropdown> = ({
    badgeContent,
    menuId,
    chipId,
    filterItem,
    label,
    onChange,
    values,
}) => {
    const [checkboxes, setCheckboxes] = useState<any>([]);
    const menu = useMenu();

    const handleCheckboxChange = (checkboxKey: string | number, isChecked: boolean) => {
        setCheckboxes((prevCheckboxes: any) =>
            prevCheckboxes.map((checkbox: any) =>
                checkbox.key === checkboxKey ? {...checkbox, checked: isChecked} : checkbox
            )
        );
    };

    useEffect(() => {
        if (values) {
            setCheckboxes(getCheckboxes(values, handleCheckboxChange));
        }
    }, [values]);

    useEffect(() => {
        onChange(filterItem, checkboxes);
    }, [checkboxes]);

    if (checkboxes.length === 0) {
        return <></>;
    }

    return (
        <>
            <DropdownChip
                testId={`${filterItem}-filter`}
                label={label}
                id={chipId}
                menuId={menuId}
                onClick={menu.handleClick}
                open={menu.open}
                badgeContent={badgeContent}
            />
            <Menu
                menuId={menuId}
                anchorEl={menu.anchorEl}
                open={menu.open}
                onClose={menu.handleClose}
            >
                <CheckboxListMenuItem
                    key="status-chechbox-list-menu-item"
                    variant="simple"
                    checkboxes={checkboxes}
                />
            </Menu>
        </>
    );
};

export default CollectionStatusFilterComponent;
