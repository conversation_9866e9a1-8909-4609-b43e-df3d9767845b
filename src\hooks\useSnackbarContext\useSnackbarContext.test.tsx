import {renderHook} from '@testing-library/react';
import React from 'react';
import useSnackbarContext from '.';
import SnackbarProvider from '../../providers/SnackbarProvider';

describe('Use Snackbar Context', () => {
    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    it('throws expected error when called outside a snackbar provider', async () => {
        const expectedError = new Error(
            'useSnackbarContext must be used within a SnackbarProvider'
        );

        await renderHook(() => {
            try {
                useSnackbarContext();
            } catch (e) {
                expect(e as Error).toEqual(expectedError);
            }
        });
    });

    it('returns snackbar context', async () => {
        const wrapper = ({children}: {children: React.ReactNode}) => (
            <SnackbarProvider>{children}</SnackbarProvider>
        );

        const {result} = await renderHook(() => useSnackbarContext(), {
            wrapper,
        });

        expect(result.current).toEqual({setSnackbar: expect.any(Function)});
    });
});
