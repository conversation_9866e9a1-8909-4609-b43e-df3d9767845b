import React, {useEffect, useState} from 'react';
import {debounce} from '@mui/material/utils';
import {Alert, convertPxToRem} from '@treez-inc/component-library';
import {Box, styled} from '@mui/material/';
import {useNavigate} from 'react-router-dom';
import AddCollection from '../../components/AddCollection';
import CollectionModal from '../../components/CollectionModal';
import {COLLECTIONS_URL} from '../../constants/apiEndPoints';
import useApiContext from '../../hooks/useApiContext';
import useSnackbarContext from '../../hooks/useSnackbarContext';
import {CollectionFormData, CollectionFormError} from '../../interfaces/collectionForm';
import {CollectionCreateDTO} from '../../interfaces/productCollection';
import collectionUtil from '../../utils/collectionForm';
import {IProductCollectionData} from '../../interfaces/productCollectionResponse';

const StyledAlert = styled(Box)(() => ({
    width: 'auto',
    display: 'flex',
    marginTop: convertPxToRem(10),
}));
interface IAddProductcollection {
    closeModal: () => void;
    productCollectionData: IProductCollectionData[];
    appendCollection: (value: IProductCollectionData) => void;
    setIsLoading: (loading: boolean) => void;
}

const AddProductCollection: React.FC<IAddProductcollection> = ({
    closeModal,
    productCollectionData,
    appendCollection,
    setIsLoading,
}) => {
    const [formData, setFormData] = useState<CollectionFormData>({collectionName: ''});
    const [isAddDisabled, setAddDisabled] = useState(false);
    const [isAlertOpen, setIsAlertOpen] = useState(false);
    const [formError, setFormError] = useState<{
        [Key in keyof CollectionFormData]: CollectionFormError;
    }>({
        collectionName: {isError: false, message: ''},
    });

    const {api} = useApiContext();
    const {setSnackbar} = useSnackbarContext();
    const navigate = useNavigate();

    const resetForm = () => setFormData({collectionName: ''});

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        closeModal();
        setAddDisabled(true);
        setIsLoading(true);

        const body: CollectionCreateDTO = {
            name: formData.collectionName,
            collectionItems: [],
            sync: false,
        };

        try {
            const {data} = await api.post(COLLECTIONS_URL, body);
            appendCollection(data);
            setSnackbar({
                message: `Collection ${data.name} is created.`,
                severity: 'info',
                iconName: 'Success',
            });
            resetForm();
            const collectionId = data?.id;
            const collectionName = data?.name;
            const encodedCollectionName = btoa(collectionName);
            navigate(`/product-collection/${collectionId}?n=${encodedCollectionName}`);
        } catch (error) {
            setSnackbar({
                message: `We can not create ${formData.collectionName} collection. Please try again later.`,
                severity: 'error',
                iconName: 'Error',
            });
        }
        setAddDisabled(false);
        setIsLoading(false);
    };

    const debounceFunction = React.useCallback(
        debounce(async (value: string, key: string) => {
            const isCollectionNameExists = collectionUtil.checkActiveCollectionNameExist(
                productCollectionData,
                value,
                undefined,
            );
            const isMaxLimitReached = collectionUtil.checkCollectionNameLength(value);
            const isNameEmpty = collectionUtil.collectionNameLengthIsEmpty(value);
            setFormError((err) => ({
                ...err,
                [key]: {
                    isError: isCollectionNameExists || isMaxLimitReached || isNameEmpty,
                    message: collectionUtil.collectionFormErrorMessage(
                        isNameEmpty,
                        isMaxLimitReached,
                        isCollectionNameExists
                    ),

                },
            }));
        }, 500),
        []
    );

    const dismissAlert = () => setIsAlertOpen(false);

    useEffect(() => {
        const isError = Object.values(formError).some((error) => error.isError);
        setAddDisabled(formData.collectionName === '' || isError);
        if (formError.collectionName.isError) {
            setIsAlertOpen(true);
        }
    }, [formData, formError]);

    return (
        <form onSubmit={handleSubmit} data-testid="add-product-collection-form">
            <CollectionModal
                isOpen
                primaryButton={{
                    onClick: handleSubmit,

                    label: 'Create',
                    isDisabled: isAddDisabled,
                }}
                secondaryButton={{
                    onClose: closeModal,
                    label: 'Cancel',
                    isDisabled: isAddDisabled,
                }}
                title="Create New Manual Collection"
                testId="add-collection-modal"
            >
                <>
                    <AddCollection
                        formData={formData}
                        setFormData={setFormData}
                        formError={formError}
                        debounceFunction={debounceFunction}
                    />
                    {isAlertOpen && formError.collectionName.isError && (
                        <StyledAlert>
                            <Alert
                                actions={[
                                    {
                                        label: 'Dismiss',
                                        onClick: () => dismissAlert(),
                                    },
                                ]}
                                message={formError.collectionName.message}
                                severity="warning"
                                testId="duplicate-collection-name-alert"
                                title="Wanning"
                            />
                        </StyledAlert>
                    )}
                </>
            </CollectionModal>
        </form>
    );
};

export default AddProductCollection;
