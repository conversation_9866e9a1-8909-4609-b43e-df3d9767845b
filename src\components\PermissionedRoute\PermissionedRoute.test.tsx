import React from 'react';
import {render} from '@testing-library/react';
import '@testing-library/jest-dom';
import {TreezThemeProvider} from '@treez-inc/component-library';
import PermissionedRoute from '.';
import {MFEPermissions} from '../../interfaces/permissions';

const testIdPageLoader = 'permissioned-route-loader';
const testIdMockNoPermission = 'no-permission';
const testIdMockPermissionedRoute = 'permissioned-route';
const orgId = 'orgId';

jest.mock('../ErrorPermissions', () => () => <div data-testid={testIdMockNoPermission} />);
jest.mock('../../hooks/usePermissionsContext');

const mockUsePermissionsContext = require('../../hooks/usePermissionsContext').default;

describe('<PermissionedRoute />', () => {
    beforeEach(() => {
        const context = {
            permissions: {},
            orgId,
        };
        mockUsePermissionsContext.mockReturnValue(context);
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    const renderPermissionedRouteComponent = (anyOfPermissions: string[]) => {
        const {queryByTestId} = render(
            <TreezThemeProvider>
                <PermissionedRoute anyOf={anyOfPermissions}>
                    <div data-testid={testIdMockPermissionedRoute} />
                </PermissionedRoute>
            </TreezThemeProvider>
        );

        const pageLoaderComponent = queryByTestId(testIdPageLoader);
        const noPermissionComponent = queryByTestId(testIdMockNoPermission);
        const permissionedRoute = queryByTestId(testIdMockPermissionedRoute);

        return {
            pageLoaderComponent,
            noPermissionComponent,
            permissionedRoute,
        };
    };

    it('should render the page loader when permissions context returns initial null values', async () => {
        const context = {
            permissions: null,
            orgId: null,
        };
        mockUsePermissionsContext.mockReturnValue(context);

        const {pageLoaderComponent, noPermissionComponent, permissionedRoute} =
            renderPermissionedRouteComponent([
                MFEPermissions.VIEW_PRODUCT_COLLECTIONS,
                MFEPermissions.MANAGE_PRODUCT_COLLECTIONS,
            ]);

        expect(mockUsePermissionsContext).toBeCalledTimes(1);
        expect(pageLoaderComponent).toBeInTheDocument();
        expect(noPermissionComponent).toBeNull();
        expect(permissionedRoute).toBeNull();
    });

    it('should render the permissions error component if user does not have any permissions', async () => {
        const context = {
            permissions: {},
            orgId,
        };
        mockUsePermissionsContext.mockReturnValue(context);

        const {pageLoaderComponent, noPermissionComponent, permissionedRoute} =
            renderPermissionedRouteComponent([
                MFEPermissions.VIEW_PRODUCT_COLLECTIONS,
                MFEPermissions.MANAGE_PRODUCT_COLLECTIONS,
            ]);

        expect(mockUsePermissionsContext).toBeCalledTimes(1);
        expect(pageLoaderComponent).toBeNull();
        expect(noPermissionComponent).toBeInTheDocument();
        expect(permissionedRoute).toBeNull();
    });

    it('renders the permissions error component if user does not have the correct permissions', async () => {
        const context = {
            permissions: {'some::permission': true},
            orgId,
        };
        mockUsePermissionsContext.mockReturnValue(context);

        const {pageLoaderComponent, noPermissionComponent, permissionedRoute} =
            renderPermissionedRouteComponent([
                MFEPermissions.VIEW_PRODUCT_COLLECTIONS,
                MFEPermissions.MANAGE_PRODUCT_COLLECTIONS,
            ]);

        expect(mockUsePermissionsContext).toBeCalledTimes(1);
        expect(pageLoaderComponent).toBeNull();
        expect(noPermissionComponent).toBeInTheDocument();
        expect(permissionedRoute).toBeNull();
    });

    it('should render the permissioned route when user has the correct permission', async () => {
        const context = {
            permissions: {
                [MFEPermissions.VIEW_PRODUCT_COLLECTIONS]: true,
                [MFEPermissions.MANAGE_PRODUCT_COLLECTIONS]: true,
            },
            orgId,
        };
        mockUsePermissionsContext.mockReturnValue(context);

        const {pageLoaderComponent, noPermissionComponent, permissionedRoute} =
            renderPermissionedRouteComponent([
                MFEPermissions.VIEW_PRODUCT_COLLECTIONS,
                MFEPermissions.MANAGE_PRODUCT_COLLECTIONS,
            ]);

        expect(mockUsePermissionsContext).toBeCalledTimes(1);
        expect(pageLoaderComponent).toBeNull();
        expect(noPermissionComponent).toBeNull();
        expect(permissionedRoute).toBeInTheDocument();
    });
});
