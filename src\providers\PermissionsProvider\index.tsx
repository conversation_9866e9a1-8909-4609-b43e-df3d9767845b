import React, {useMemo, createContext, useState, useEffect} from 'react';
import IFrameworkProps from '../../interfaces/frameworkProps';
import {IPermissionsContext} from '../../interfaces/permissions';

export const PermissionsContext = createContext<IPermissionsContext | null>(null);

export interface IUserPermissionsProviderProps {
    children: React.ReactNode;
    getPermissions: IFrameworkProps['getPermissions'];
}

const PermissionsProvider: React.FC<IUserPermissionsProviderProps> = ({
    children,
    getPermissions,
}) => {
    const [contextProvider, setContextProvider] = useState<IPermissionsContext>({
        permissions: null,
        orgId: null,
    });

    useEffect(() => {
        const getPermissionsAsync = async () => {
            const response = await getPermissions();

            if (response !== null) {
                const {permissions, id} = response;
                setContextProvider({
                    permissions: Object.assign(
                        {},
                        ...permissions.map((p: string) => ({[p]: true}))
                    ),
                    orgId: id,
                });
            }
        };
        getPermissionsAsync();
    }, []);

    const provider = useMemo(() => contextProvider, [Object.values(contextProvider)]);

    return <PermissionsContext.Provider value={provider}>{children}</PermissionsContext.Provider>;
};

export default PermissionsProvider;
