import { useQuery } from 'react-query';
import { UseQueryOptions } from 'react-query/types/react/types';
import { getData } from '../api/genericAccessor';
import { AttributeCategoryData } from '../interfaces/attributeCategoryData';
import Entities from '../interfaces/entities.enum';
import { PRODUCT_CONTROL_API_URL } from '../constants/apiEndPoints';

const useGetAttributeCategoriesOptions  = (options?: UseQueryOptions): { data: AttributeCategoryData[]; isLoading: boolean } => {
    const { data, isLoading } = useQuery({
        queryKey: ['product-category-attribute'],
        queryFn: async () => {
            const result: AttributeCategoryData[] = await getData(PRODUCT_CONTROL_API_URL, Entities.ATTRIBUTE_CATEGORY, {});
            return result;
        },
        ...options,
    });
    return { data: data as AttributeCategoryData[], isLoading };
};

export default useGetAttributeCategoriesOptions;