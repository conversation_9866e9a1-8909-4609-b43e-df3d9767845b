import {AxiosInstance} from 'axios';
import apiInterceptor from '.';

jest.mock('../retryRequest');
const mockRetryRequest = require('../retryRequest').default;

const mockGetTokens = jest.fn();
const mockRefreshTokens = jest.fn();
const mockRedirectToLogin = jest.fn();

const freshAccessToken = 'accessToken';
const apiUrl = 'dummyUrl';
describe('Api Interceptor', () => {
    beforeEach(() => {
        mockGetTokens.mockReturnValue({accessToken: freshAccessToken});
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    it('should create expected axios instance', async () => {
        const api: AxiosInstance = apiInterceptor(
            mockGetTokens,
            mockRefreshTokens,
            mockRedirectToLogin
        );

        expect(api).toHaveProperty('get');
        expect(api).toHaveProperty('post');
        expect(api).toHaveProperty('put');
        expect(api).toHaveProperty('delete');
        expect(api).toHaveProperty('interceptors');
        expect(api).toHaveProperty('create');
    });

    it('should retry request when network error occurs', async () => {
        const errorObj = {
            message: 'Network Error',
            code: '1',
            config: {
                headers: {
                    retry: '1',
                    Authorization: 'oldAccessToken',
                    toJSON: jest.fn(),
                },
                url: apiUrl,
            },
            request: {},
            response: {},
        };

        const expectedNewHeaders = {
            ...errorObj.config.headers,
            retry: '2',
            Authorization: freshAccessToken,
        };

        const api: AxiosInstance = apiInterceptor(
            mockGetTokens,
            mockRefreshTokens,
            mockRedirectToLogin
        );
        // @ts-ignore
        await api.interceptors.response.handlers[0].rejected(errorObj);

        expect(mockRetryRequest).toBeCalledWith(errorObj.config, api, expectedNewHeaders);
        expect(mockRetryRequest).toBeCalledTimes(1);
        expect(mockRefreshTokens).toBeCalledTimes(0);
        expect(mockRedirectToLogin).toBeCalledTimes(0);
    });

    it('should retry request when timeout error occurs', async () => {
        const errorObj = {
            message: 'timeout',
            code: '1',
            config: {
                headers: {
                    retry: '1',
                    Authorization: 'oldAccessToken',
                    toJSON: jest.fn(),
                },
                url: apiUrl,
            },
            request: {},
            response: {},
        };

        const expectedNewHeaders = {
            ...errorObj.config.headers,
            retry: '2',
            Authorization: freshAccessToken,
        };

        const api: AxiosInstance = apiInterceptor(
            mockGetTokens,
            mockRefreshTokens,
            mockRedirectToLogin
        );
        // @ts-ignore
        await api.interceptors.response.handlers[0].rejected(errorObj);

        expect(mockRetryRequest).toBeCalledWith(errorObj.config, api, expectedNewHeaders);
        expect(mockRetryRequest).toBeCalledTimes(1);
        expect(mockRefreshTokens).toBeCalledTimes(0);
        expect(mockRedirectToLogin).toBeCalledTimes(0);
    });

    it('should throw error if retry is greater than 3', async () => {
        const config = {
            message: 'Network Error',
            code: '1',
            config: {
                headers: {
                    retry: '4',
                },
            },
            request: {},
            response: {},
        };

        try {
            const api: AxiosInstance = apiInterceptor(
                mockGetTokens,
                mockRefreshTokens,
                mockRedirectToLogin
            );
            // @ts-ignore
            await api.interceptors.response.handlers[0].rejected(config);
        } catch (err) {
            expect(err).toEqual(config);
            expect(mockRetryRequest).toBeCalledTimes(0);
            expect(mockRefreshTokens).toBeCalledTimes(0);
            expect(mockRedirectToLogin).toBeCalledTimes(0);
        }
    });

    it('should throw error if error is not network or timeout error', async () => {
        const config = {
            message: 'some error',
            code: '1',
            config: {
                headers: {
                    retry: '0',
                },
            },
            request: {},
            response: {},
        };

        try {
            const api: AxiosInstance = apiInterceptor(
                mockGetTokens,
                mockRefreshTokens,
                mockRedirectToLogin
            );
            // @ts-ignore
            await api.interceptors.response.handlers[0].rejected(config);
        } catch (err) {
            expect(err).toEqual(config);
            expect(mockRetryRequest).toBeCalledTimes(0);
            expect(mockRefreshTokens).toBeCalledTimes(0);
            expect(mockRedirectToLogin).toBeCalledTimes(0);
        }
    });

    it('should throw error if there is no retry header', async () => {
        const config = {
            message: 'some error',
            code: '1',
            config: {headers: {}},
            request: {},
            response: {},
        };

        try {
            const api: AxiosInstance = apiInterceptor(
                mockGetTokens,
                mockRefreshTokens,
                mockRedirectToLogin
            );
            // @ts-ignore
            await api.interceptors.response.handlers[0].rejected(config);
        } catch (err) {
            expect(err).toEqual(config);
            expect(mockRetryRequest).toBeCalledTimes(0);
            expect(mockRefreshTokens).toBeCalledTimes(0);
            expect(mockRedirectToLogin).toBeCalledTimes(0);
        }
    });

    it('should refresh token if status code is 401', async () => {
        const config = {
            message: 'some error',
            code: '1',
            config: {headers: {}},
            request: {},
            response: {status: 401},
        };

        try {
            const api: AxiosInstance = apiInterceptor(
                mockGetTokens,
                mockRefreshTokens,
                mockRedirectToLogin
            );
            // @ts-ignore
            await api.interceptors.response.handlers[0].rejected(config);
        } catch (err) {
            expect(err).toEqual(config);
            expect(mockRetryRequest).toBeCalledTimes(0);
            expect(mockRefreshTokens).toBeCalledTimes(1);
            expect(mockRedirectToLogin).toBeCalledTimes(0);
        }
    });

    it('should throw error if there is no retry header and timeout error occurred', async () => {
        const config = {
            message: 'timeout',
            code: '1',
            config: {headers: {}},
            request: {},
            response: {},
        };

        try {
            const api: AxiosInstance = apiInterceptor(
                mockGetTokens,
                mockRefreshTokens,
                mockRedirectToLogin
            );
            // @ts-ignore
            await api.interceptors.response.handlers[0].rejected(config);
        } catch (err) {
            expect(err).toEqual(config);
            expect(mockRetryRequest).toBeCalledTimes(0);
            expect(mockRefreshTokens).toBeCalledTimes(0);
            expect(mockRedirectToLogin).toBeCalledTimes(0);
        }
    });

    it('should redirect to login if status code is 401 and retry is greater than 3', async () => {
        const config = {
            message: 'some error',
            code: '1',
            config: {
                headers: {
                    retry: '4',
                },
            },
            request: {},
            response: {status: 401},
        };

        try {
            const api: AxiosInstance = apiInterceptor(
                mockGetTokens,
                mockRefreshTokens,
                mockRedirectToLogin
            );
            // @ts-ignore
            await api.interceptors.response.handlers[0].rejected(config);
        } catch (err) {
            expect(err).toEqual(config);
            expect(mockRetryRequest).toBeCalledTimes(0);
            expect(mockRefreshTokens).toBeCalledTimes(1);
            expect(mockRedirectToLogin).toBeCalledTimes(1);
        }
    });
});
