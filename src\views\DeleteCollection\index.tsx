import React, {useState} from 'react';
import CollectionModal from '../../components/CollectionModal';
import IProductCollection from '../../interfaces/productCollection';
import useApiContext from '../../hooks/useApiContext';
import DeleteCollection from '../../components/DeleteCollection';
import {COLLECTIONS_URL} from '../../constants/apiEndPoints';
import useSnackbarContext from '../../hooks/useSnackbarContext';

interface IDeleteCollectionView {
    collection: IProductCollection;
    deleteCollection: (collection: IProductCollection) => void;
    closeModel: () => void;
    setIsLoading: (loading: boolean) => void;
}

const DeleteCollectionView: React.FC<IDeleteCollectionView> = ({
    collection,
    deleteCollection,
    closeModel,
    setIsLoading,
}) => {
    const {api} = useApiContext();
    const [deleteButtonDisable, setDeleteButton] = useState(false);
    const {setSnackbar} = useSnackbarContext();

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        closeModel();
        setDeleteButton(true);
        setIsLoading(true);

        try {
            const result = await api.delete(`${COLLECTIONS_URL}/${collection.id}`);
            const deactivatedCollectionData: IProductCollection = result.data;
            deleteCollection(deactivatedCollectionData);
        } catch (error) {
            setSnackbar({
                message: `We can not delete ${collection.name} collection. Please try again later.`,
                severity: 'error',
                iconName: 'Error',
            });
        }
        setIsLoading(false);
    };

    return (
        <CollectionModal
            isOpen
            primaryButton={{
                onClick: handleSubmit,
                label: 'Yes, Delete',
                isDisabled: deleteButtonDisable,
            }}
            secondaryButton={{
                onClose: closeModel,
                label: 'Cancel',
                isDisabled: deleteButtonDisable,
            }}
            title="Are you sure?"
            testId="delete-collection-modal"
        >
            <DeleteCollection name={collection.name} testid="delete-message-container" />
        </CollectionModal>
    );
};

export default DeleteCollectionView;
