import React from 'react';
import {ErrorTemplate, Link} from '@treez-inc/component-library';
import CenteredContent from '../CenteredContent';

const ErrorDefault: React.FC = () => (
    <CenteredContent testId="error-default">
        <ErrorTemplate
            hideLogo
            title="Hmm… Something went wrong"
            body={
                <p>
                    We&apos;re having trouble loading this page. Refresh to try again. If the issue
                    persists please contact{' '}
                    <Link
                        href="https://support.treez.io/hc/en-us"
                        ariaLabel="link that navigates to Treez support center"
                    >
                        Customer Support
                    </Link>
                    .
                </p>
            }
            buttonProps={{
                label: 'Refresh',
                onClick: () => {
                    window.location.reload();
                },
            }}
            testId="error-template"
        />
    </CenteredContent>
);

export default ErrorDefault;

// 500
