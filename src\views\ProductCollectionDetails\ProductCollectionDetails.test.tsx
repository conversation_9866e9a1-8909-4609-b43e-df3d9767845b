import React from 'react';
import {render, screen} from '@testing-library/react';
import '@testing-library/jest-dom';
import {TreezThemeProvider} from '@treez-inc/component-library';
import {createMemoryHistory} from 'history';
import {MemoryRouter} from 'react-router-dom';
import {productListData} from '../../test/testData';
import ApiService from '../../utils/apiService';
import SnackbarProvider from '../../providers/SnackbarProvider';
import ProductCollectionDetails from '.';

const testIdCollectionPageLayout = 'collection-details-page-layout';
const testIdCollectionDetailsGridView = 'collection-details-grid-layout';
const testIdColumnHeader = 'columnheader';
const testIdRows = 'rows';
const testIdLoadingSpinner = 'loading-spinner';

jest.mock('../../hooks/useProductCollectionDetails');
jest.mock('../../constants/routes');
jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useParams: () => ({
        id: '123',
    }),
}));
jest.mock('../../hooks/useApiContext');
jest.mock('../../components/RouteError', () => () => <div data-testid="mock-route-error" />);
jest.mock('../../hooks/usePermissionsContext');
const mockUserPermissionsContext = require('../../hooks/usePermissionsContext').default;

const orgId = '4a873b6d-e350-4955-a254-bb37b8bc81f6';
const permission = {
    permissions: {},
    orgId,
};

const mockUseProductCollectionDetails = require('../../hooks/useProductCollectionDetails').default;
const mockUseApiContext = require('../../hooks/useApiContext').default;

const validTokens = {
    accessToken: 'accessToken',
    expiresIn: 300,
    refreshToken: 'refreshToken',
    idToken: 'idToken',
};
const clearTokens = jest.fn();
const getTokens = () => validTokens;
const redirectToLogin = jest.fn();
const apiService = new ApiService(getTokens, clearTokens, redirectToLogin);

describe('<ProductCollectionDetails />', () => {
    beforeEach(() => {
        mockUseProductCollectionDetails.mockReturnValue({
            loading: false,
            error: null,
            data: productListData,
        });
        mockUseApiContext.mockImplementation(() => apiService);
        mockUserPermissionsContext.mockImplementation(() => permission);
    });

    const renderProductCollectionDetailsView = () => {
        const history = createMemoryHistory();
        const {queryByTestId, queryAllByRole} = render(
            <MemoryRouter>
                <TreezThemeProvider>
                    <SnackbarProvider>
                        <ProductCollectionDetails />
                    </SnackbarProvider>
                </TreezThemeProvider>
            </MemoryRouter>
        );

        const productCollectionDetailsView = queryByTestId(testIdCollectionPageLayout);
        const productCollectionDetailsListWrapper = queryByTestId(testIdCollectionDetailsGridView);
        const columnHeaders = queryAllByRole(testIdColumnHeader);
        const rows = queryAllByRole(testIdRows);

        return {
            productCollectionDetailsView,
            productCollectionDetailsListWrapper,
            columnHeaders,
            rows,
            history,
        };
    };

    it('should render product collections detail view', () => {
        const {productCollectionDetailsView, productCollectionDetailsListWrapper} =
            renderProductCollectionDetailsView();

        expect(productCollectionDetailsView).toBeInTheDocument();
        expect(productCollectionDetailsListWrapper).toBeInTheDocument();
    });

    it('handles error during decoding of collection name', () => {
        const mockSearchParams = new URLSearchParams('?n=invalid-base64');
        jest.spyOn(window, 'URLSearchParams').mockImplementation(() => mockSearchParams);
        const consoleSpy = jest.spyOn(console, 'debug').mockImplementation();

        renderProductCollectionDetailsView();

        expect(consoleSpy).toHaveBeenCalledWith(
            expect.stringContaining('Error occured on decoding collection name'),
            expect.any(Error)
        );
        consoleSpy.mockRestore();
    });

    it('renders RouteError when useProductCollectionDetails returns an error', () => {
        const mockError = new Error('Data fetching error');
        mockUseProductCollectionDetails.mockReturnValue({
            loading: false,
            error: mockError,
            data: null,
        });

        renderProductCollectionDetailsView();

        expect(screen.getByTestId('mock-route-error')).toBeInTheDocument();
        expect(screen.queryByTestId('collection-details-page-layout')).not.toBeInTheDocument();
    });

    it('renders empty array when useProductCollectionDetails returns null data', () => {
        mockUseProductCollectionDetails.mockReturnValue({data: null, loading: false, error: null});

        renderProductCollectionDetailsView();

        expect(screen.getByTestId('collection-details-grid-layout')).toBeInTheDocument();
        const gridElement = screen.getByTestId('collection-details-grid-layout');
        const dataProp = JSON.parse(gridElement.getAttribute('data') || '[]');
        expect(dataProp).toEqual([]);
    });

    it('render LoadingSpinner', () => {
        mockUseProductCollectionDetails.mockReturnValue({
            loading: true,
            error: null,
            data: productListData,
        });

        renderProductCollectionDetailsView();

        expect(screen.getByTestId(testIdLoadingSpinner)).toBeInTheDocument();
    });
});
