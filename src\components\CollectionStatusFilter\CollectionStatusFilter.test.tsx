import React from 'react';
import { render, fireEvent, screen, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import { TreezThemeProvider } from '@treez-inc/component-library';
import CollectionStatusFilterComponent from '.';
import CollectionStatus from '../../interfaces/collectionStatus.enum';
import { CollectionStatusCheckbox } from '../../interfaces/collectionFilter';

describe('CollectionStatusFilterComponent', () => {
    let onFilter: jest.Mock<any, any>;
    let mockFilterItem: string;
    let mockLabel: string;
    let mockStatus: CollectionStatusCheckbox[];

    beforeEach(() => {
        onFilter = jest.fn();
        mockFilterItem = 'status';
        mockLabel = 'Status';
        mockStatus = [
            {
                key: CollectionStatus.ACTIVE,
                value: CollectionStatus.ACTIVE,
                label: 'Active',
                checked: true,
            },
            {
                key: CollectionStatus.INACTIVE,
                value: CollectionStatus.INACTIVE,
                label: 'Inactive',
                checked: false,
            },
        ];
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    const renderComponent = (values?: CollectionStatusCheckbox[]) => {
        render(
            <TreezThemeProvider>
                <CollectionStatusFilterComponent
                    filterItem={mockFilterItem}
                    label={mockLabel}
                    values={values}
                    chipId="mockChipId"
                    menuId="mockMenuId"
                    onChange={onFilter}
                />
            </TreezThemeProvider>
        );
    };

    it('renders the component with default values', () => {
        renderComponent(mockStatus);
    });

    it('handles checkbox change', () => {
        renderComponent(mockStatus);

        const statusFilter = screen.getByTestId('status-filter');
        act(() => {
            fireEvent.click(statusFilter);
        });

        const menu = screen.getByRole('menu');
        const labels = menu.querySelectorAll('label');

        expect(menu).toBeInTheDocument();
        expect(labels.length).toBe(2);

        act(() => {
            fireEvent.click(labels[0]);
        });

        expect(onFilter).toBeCalled();
    });
    it('handles checkbox change', () => {
        renderComponent(undefined);
    });
});
