/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import {fireEvent, render, screen, waitFor} from '@testing-library/react';
import '@testing-library/jest-dom';
import {TreezThemeProvider} from '@treez-inc/component-library';
import {CollectionMenu} from '.';
import IProductCollection from '../../interfaces/productCollection';
import SnackbarProvider from '../../providers/SnackbarProvider';

jest.mock('../../hooks/usePermissionsContext');
jest.mock('../../hooks/useApiContext');

const mockedUseNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
    useNavigate: () => mockedUseNavigate,
}));

const mockUserPermissionsContext = require('../../hooks/usePermissionsContext').default;
const mockUseApiContext = require('../../hooks/useApiContext').default;

const orgId = '28195885-d76d-40c5-acb8-bf3ebddd2259';

jest.mock('../../views/DeleteCollection/index');

const mockDeleteCollectionView = require('../../views/DeleteCollection/index').default;

const mockApi = jest.fn();
const setIsLoading = jest.fn();
const mockPermission = {
    permissions: {},
    orgId,
};

const collectionData = {
    collection: {
        id: 'id',
        name: 'test name',
        createdAt: '2023-11-06T12:50:03.584Z',
        updatedAt: '2023-11-06T12:50:03.584Z',
        deletedAt: null,
    } as IProductCollection,
};

describe('<CollectionMenu />', () => {
    beforeEach(() => {
        mockUseApiContext.mockImplementation(() => mockApi);
        mockUserPermissionsContext.mockImplementation(() => mockPermission);
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    const renderCollectionMenuComponent = ({collection} = collectionData) => {
        const {queryByTestId} = render(
            <TreezThemeProvider>
                <SnackbarProvider>
                    <CollectionMenu
                        row={collection}
                        deleteCollection={() => {}}
                        setIsLoading={setIsLoading}
                    />
                </SnackbarProvider>
            </TreezThemeProvider>
        );

        const icon = queryByTestId('collections-flyout-menu-more');
        const menu = queryByTestId('collection-action-menu');

        return {
            icon,
            menu,
        };
    };

    it('renders with menu closed', () => {
        const {icon, menu} = renderCollectionMenuComponent();

        expect(icon).toBeInTheDocument();
        expect(menu).toBe(null);
    });

    it('opens menu when icon is clicked', async () => {
        const {icon} = renderCollectionMenuComponent();
        fireEvent.click(icon as HTMLElement);

        const deleteCollectionItem = screen.getByTestId('collection-action-delete');

        expect(deleteCollectionItem).toBeVisible();
    });

    it('closes menu when menu item is clicked', async () => {
        const {icon, menu} = renderCollectionMenuComponent();
        fireEvent.click(icon as HTMLElement);

        await waitFor(() => {
            expect(menu).toEqual(null);
        });
    });

    it('Should collection delete model pop up when Delete Collection is clicked', async () => {
        const {icon} = renderCollectionMenuComponent();

        fireEvent.click(icon as HTMLElement);
        const deleteCollectionItem = await screen.getByTestId('collection-action-delete');

        fireEvent.click(deleteCollectionItem as HTMLElement);
        expect(mockDeleteCollectionView).toBeCalledTimes(1);
    });
});
