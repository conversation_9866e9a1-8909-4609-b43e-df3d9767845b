import React from "react";
import { styled } from "@mui/material";
import { CircularProgress } from "@treez-inc/component-library";

const PageLoadingBackground = styled("div")(({ theme }) => ({
  position: "fixed",
  opacity: "0.7",
  backgroundColor: theme.palette.primaryWhite.main,
  width: "100%",
  top: 0,
  right: 0,
  bottom: 0,
  left: 0,
  zIndex: 999,
}));

const LoadingSpinnerContainer = styled("div")({
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  height: "100%",
});

const LoadingSpinner: React.FC = () => (
  <PageLoadingBackground>
    <LoadingSpinnerContainer>
      <CircularProgress testId="loading-spinner" color="green06" />
    </LoadingSpinnerContainer>
  </PageLoadingBackground>
);

export default LoadingSpinner;