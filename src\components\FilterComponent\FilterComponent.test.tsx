import React from 'react';
import {act, fireEvent, render, screen} from '@testing-library/react';
import '@testing-library/jest-dom';
import {TreezThemeProvider} from '@treez-inc/component-library';
import FilterComponent from '.';
import {CollectionStatusCheckbox} from '../../interfaces/collectionFilter';
import CollectionStatus from '../../interfaces/collectionStatus.enum';

describe('Filter Component', () => {
    let onFilter: jest.Mock<any, any>;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    let collectionStatus: CollectionStatusCheckbox[];

    beforeEach(() => {
        onFilter = jest.fn();
        collectionStatus = [
            {
                key: CollectionStatus.ACTIVE,
                value: CollectionStatus.ACTIVE,
                label: 'Active',
                checked: true,
            },
            {
                key: CollectionStatus.INACTIVE,
                value: CollectionStatus.INACTIVE,
                label: 'Inactive',
                checked: false,
            },
        ];
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    const renderComponent = () => {
        render(
            <TreezThemeProvider>
                <FilterComponent onChange={onFilter} />
            </TreezThemeProvider>
        );
    };

    it('renders the component', () => {
        renderComponent();

        const statusFilter = screen.getByTestId('collection-status-filter');
        expect(statusFilter).toBeInTheDocument();
    });

    it('handle filter change', () => {
        renderComponent();

        const statusFilter = screen.getByTestId('collection-status-filter');
        act(() => {
            fireEvent.click(statusFilter);
        });

        const menu = screen.getByRole('menu');
        const labels = menu.querySelectorAll('label');

        expect(menu).toBeInTheDocument();
        expect(labels.length).toBe(2);

        act(() => {
            fireEvent.click(labels[0]);
        });

        expect(onFilter).toBeCalled();
    });
});
