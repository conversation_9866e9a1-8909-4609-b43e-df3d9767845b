import React, {useEffect, useState} from 'react';
import {Button, convertPxToRem} from '@treez-inc/component-library';
import {Box, Grid, styled} from '@mui/material/';
import {useFieldArray, useFormContext} from 'react-hook-form';
import {
    AUTOMATED_COLLECTION_RULES,
    PRODUCT_CLASSIFICATION_OPTIONS,
    PRODUCT_TYPE_OPTIONS,
} from '../../../constants/productCollection';
import AdditionalFilterComponent from './AdditionalFilterComponent';
import {ProductCategoryDto} from '../../../interfaces/dto/productCategory';
import {priceCentsToDollar, priceDollarToCents} from '../../../utils/common';
import {AutomatedCollectionRulesForm} from '../../../interfaces/attributeCategoryData';
import {EXCLUDED_SUB_CATEGORY_BY_CATEGORY} from '../../../utils/constants';
import {AutomatedCollectionDetails} from '../../../interfaces/collectionForm';

export const AutomatedCollectionFilterField = styled(Box)({
    padding: `${convertPxToRem(8)} 0`,
});

const AdditionalFilterContainer = styled(Box)({
    alignItems: 'center',
    columnGap: convertPxToRem(10),
    display: 'flex',
    flexDirection: 'row',
});

const ProductCollectionRulesContainer = styled(Box)({
    display: 'flex',
    flexDirection: 'column',
    rowGap: convertPxToRem(16),
});

export const FormItem = styled(Grid)({
    padding: convertPxToRem(16),
});

export type SelectListOption = {
    key: string;
    label: string;
    checked?: boolean;
};

interface RulesComponentProps {
    fieldName: string;
    selectedCategory: ProductCategoryDto;
    setSelectedRules: React.Dispatch<React.SetStateAction<any>>;
    collectionDetailsData: any;
    attributeCategoriesData: any;
    subCategoriesData: any;
    brandsData: any;
    excludeSelectionCheckedRef: React.MutableRefObject<boolean>;
    setIsUpdatePending: React.Dispatch<React.SetStateAction<boolean>>;
}

interface RuleOption {
    id: string;
    name: string;
}
export interface Rule {
    name: string;
    ruleType: string;
    inputType: string;
    options: RuleOption[];
    values: RuleOption[];
}

interface RuleSelectedOptions {
    displayName: string;
    displayValue: string;
}

const ProductCollectionRules = ({
    fieldName,
    selectedCategory,
    setSelectedRules,
    collectionDetailsData,
    attributeCategoriesData,
    subCategoriesData,
    brandsData,
    excludeSelectionCheckedRef,
    setIsUpdatePending,
}: RulesComponentProps) => {
    const {control, getValues, setValue, reset} = useFormContext();
    const additionalFilterField = `${fieldName}.additionalFilter`;
    const additionalFilteredData: any[] = getValues(additionalFilterField);
    const [isAdditionalFilterDisabled, setAdditionalFilterDisabled] = useState<boolean>(true);

    const {
        fields: additionalFilter,
        append: addAdditionalFilter,
        remove: removeAdditionalFilter,
    } = useFieldArray({
        control,
        name: additionalFilterField,
        keyName: 'refId',
    });

    const updateRuleOptions = (rule: any, options: any) => {
        if (rule) {
            /* eslint-disable no-param-reassign */
            rule.options = options.map((option: any) => ({
                id: option.id,
                name: option.name,
            }));
        }
    };

    const setBrandRuleOptions = (attributeBrands: any) => {
        const brandRule = AUTOMATED_COLLECTION_RULES.find((rule) => rule.name === 'Brand');
        updateRuleOptions(brandRule, attributeBrands);
    };

    const setAttributeRulesOptions = (attributeCategories: any) => {
        attributeCategories.forEach((category: any) => {
            const rule = AUTOMATED_COLLECTION_RULES.find((item) => item.name === category.name);
            updateRuleOptions(rule, category.attributes);
        });
    };

    const setSubtypeRuleOptions = (attributeSubCategories: any) => {
        const subcategoryRule = AUTOMATED_COLLECTION_RULES.find(
            (rule) => rule.name === 'Subcategory'
        );
        const subCategoriesByCategory =
            selectedCategory.id === 'global'
                ? attributeSubCategories
                : attributeSubCategories.filter(
                      (item: any) => item.productCategoryId === selectedCategory.id
                  );

        // NOTE: THIS FILTER IS TEMPORAL. WE ARE GOING TO REMOVE THIS ONCE WE FIX THE MIX FROM CATALOG
        updateRuleOptions(
            subcategoryRule,
            subCategoriesByCategory.filter(
                (item: any) =>
                    EXCLUDED_SUB_CATEGORY_BY_CATEGORY[selectedCategory.name]?.indexOf(item.name) < 0
            )
        );
    };

    const setUOMRuleOptions = (category: any) => {
        const uomRule = AUTOMATED_COLLECTION_RULES.find((rule) => rule.name === 'UOM');
        if (uomRule && category?.uoms) {
            uomRule.options = category.uoms.map((uom: any) => ({
                id: uom.value,
                name: uom.label,
            }));
        }
    };

    const setClassificationRuleOptions = () => {
        const classificationRule = AUTOMATED_COLLECTION_RULES.find(
            (rule) => rule.name === 'Classification'
        );
        if (classificationRule) {
            classificationRule.options = PRODUCT_CLASSIFICATION_OPTIONS.map((classification) => ({
                id: classification.displayValue,
                name: classification.displayName,
            }));
        }
    };

    const setSizeRuleOptions = (category: any) => {
        const sizeUomRule = AUTOMATED_COLLECTION_RULES.find((rule) => rule.name === 'Amount');
        if (sizeUomRule && category?.uoms) {
            sizeUomRule.options = category.uoms.map((sizeUom: any) => ({
                id: sizeUom.value,
                name: sizeUom.label,
            }));
        }
    };

    const setSKUTypeRuleOptions = () => {
        const classificationRule = AUTOMATED_COLLECTION_RULES.find(
            (rule) => rule.ruleType === 'skuType'
        );
        if (classificationRule) {
            classificationRule.options = PRODUCT_TYPE_OPTIONS.map((obj) => ({
                id: obj.displayValue,
                name: obj.displayName,
            }));
        }
    };

    const updateSpecificDefaultValue = async () => {
        setValue(additionalFilterField, []);
        const defaultValues = getValues();
        const filteredRuleNames: string[] = [];
        Object.keys(collectionDetailsData.rule).forEach((key) => {
            const ruleType = key;
            const rule = AUTOMATED_COLLECTION_RULES.find((item) => item.ruleType === ruleType);
            if (rule) {
                const options = collectionDetailsData.rule[key]
                    .map((value: any) => {
                        let displayValue;
                        let displayName;
                        const option = rule.options.find((item: any) => item.id === value);

                        if (option) {
                            displayValue = option.id;
                            displayName = option.name;
                        } else if (ruleType === 'basePrice') {
                            displayValue = priceCentsToDollar(value);
                            displayName = priceCentsToDollar(value);
                        } else {
                            displayValue = value;
                            displayName = value;
                        }

                        return {displayValue, displayName};
                    })
                    .filter(Boolean);
                defaultValues[`${rule.name}`] = options;
            }
        });

        if (collectionDetailsData.rule) {
            const ruleNames = Object.keys(collectionDetailsData.rule);
            ruleNames.forEach((ruleType) => {
                const rule = AUTOMATED_COLLECTION_RULES.find((item) => item.ruleType === ruleType);
                if (rule) {
                    filteredRuleNames.push(rule.name);
                }
            });
        }

        addAdditionalFilter(filteredRuleNames);
        reset(defaultValues);
    };

    useEffect(() => {
        if (attributeCategoriesData && subCategoriesData && brandsData) {
            setBrandRuleOptions(brandsData);
            setAttributeRulesOptions(attributeCategoriesData);
            setSubtypeRuleOptions(subCategoriesData);
            setUOMRuleOptions(selectedCategory);
            setClassificationRuleOptions();
            setSizeRuleOptions(selectedCategory);
            setSKUTypeRuleOptions();
        }

        if (additionalFilteredData?.includes('Subcategory')) {
            const filtered = additionalFilteredData.filter((item) => item !== 'Subcategory');
            // This resets just the subcategory rule so when selected again it resets it's selected values
            setValue('Subcategory', []);
            // This is keeping all selected rules except Subcategory which gets removed when switching between Category filters
            setValue(additionalFilterField, filtered);
        }
    }, [selectedCategory]);

    useEffect(() => {
        if (collectionDetailsData) {
            updateSpecificDefaultValue();
        }
    }, [collectionDetailsData]);

    function processFilteredValues(addedRulesFilters: any, ruleData: any, rules: Rule[]) {
        const selectedRules: {[key: string]: string[]} = {};

        if (selectedCategory.id !== 'global') {
            selectedRules.category = [selectedCategory.id];
        }

        addedRulesFilters.forEach((ruleName: string) => {
            if (ruleData && ruleData[ruleName]) {
                const rule = rules.find((item) => item.name === ruleName);
                const values = ruleData[ruleName].map(
                    (item: RuleSelectedOptions) => item.displayValue
                );
                if (rule) {
                    if (rule?.ruleType === 'basePrice') {
                        const formatedAmount = values.map((value: any) =>
                            String(priceDollarToCents(value))
                        );
                        selectedRules[rule.ruleType] = formatedAmount;
                    } else {
                        selectedRules[rule.ruleType] = values;
                    }
                }
            }
        });

        return selectedRules;
    }

    const handleMultiSelectChange = async (selectedRuleCategory1: string, ruleValue: any) => {
        excludeSelectionCheckedRef.current = false;
        const additionalFilterData: any[] = getValues(additionalFilterField);
        const formDefaultValues: any = getValues();
        formDefaultValues[selectedRuleCategory1] = ruleValue;
        const selectedRules = processFilteredValues(
            additionalFilterData,
            formDefaultValues,
            AUTOMATED_COLLECTION_RULES
        );
        setSelectedRules(selectedRules);
        setAdditionalFilterDisabled(false);
        setIsUpdatePending(true);
    };

    const removeAddedRulesFilter = (ruleName: string) => {
        excludeSelectionCheckedRef.current = false;
        const formDefaultValues: any = getValues();
        delete formDefaultValues[ruleName];
        reset(formDefaultValues);
        if (formDefaultValues?.rules?.additionalFilter.length === 0) {
            addAdditionalFilter('');
        }
        setIsUpdatePending(true);
    };

    const isCollectionRulesEmpty = (collectionData: AutomatedCollectionDetails) => {
        if (!collectionData || !collectionData.rule) {
            return true;
        }

        const ruleKeys = Object.keys(collectionData.rule);

        if (ruleKeys.length === 1 && ruleKeys[0] === 'category') {
            return true;
        }

        if (ruleKeys.length === 1 && ruleKeys[0] !== 'category') {
            return false;
        }

        if (ruleKeys.length > 1) {
            return false;
        }

        return true;
    };

    useEffect(() => {
        if (additionalFilter.length === 0 && isCollectionRulesEmpty(collectionDetailsData)) {
            addAdditionalFilter('');
            setAdditionalFilterDisabled(true);
        }

        const additionalFilterData: any[] = getValues(additionalFilterField);
        const formDefaultValues: any = getValues();
        const selectedRules = additionalFilterData
            ? processFilteredValues(
                  additionalFilterData,
                  formDefaultValues,
                  AUTOMATED_COLLECTION_RULES
              )
            : [];
        setSelectedRules(selectedRules);
    }, [additionalFilter]);

    useEffect(() => {
        if (Array.isArray(additionalFilteredData)) {
            const enableAdditionalFilter =
                additionalFilteredData.some(
                    (element: any) => typeof element === 'string' && element !== ''
                ) &&
                additionalFilteredData.every(
                    (element: any) => element !== '' && element !== undefined
                );
            setAdditionalFilterDisabled(!enableAdditionalFilter);
        } else {
            setAdditionalFilterDisabled(false);
        }
    }, [additionalFilteredData]);

    const filteredCollectionRules =
        selectedCategory.id === 'global'
            ? AUTOMATED_COLLECTION_RULES.filter((cr) => cr.ruleType !== 'subCategory')
            : AUTOMATED_COLLECTION_RULES;

    return (
        <>
            <AutomatedCollectionFilterField>
                <ProductCollectionRulesContainer>
                    {additionalFilter.length > 0 &&
                        additionalFilter.map((field, index) => {
                            const selectedRuleCategory = getValues(additionalFilterField)[index];
                            const selectedRuleFilter: AutomatedCollectionRulesForm =
                                filteredCollectionRules?.find(
                                    (category) => category.name === selectedRuleCategory
                                );
                            let selectedMenuItems: any = [] || undefined;
                            if (selectedRuleFilter) {
                                const newMenuItems =
                                    selectedRuleFilter.options
                                        ?.map((attribute) => ({
                                            displayName: attribute.name,
                                            displayValue: attribute.id,
                                        }))
                                        .sort((a, b) =>
                                            a.displayName.localeCompare(b.displayName)
                                        ) || [];
                                selectedMenuItems = newMenuItems;
                            } else {
                                selectedMenuItems = undefined;
                            }

                            return (
                                <AdditionalFilterComponent
                                    fieldName={additionalFilterField}
                                    index={index}
                                    key={field.refId}
                                    remove={removeAdditionalFilter}
                                    attributeCategories={filteredCollectionRules.sort((a, b) =>
                                        a.name.localeCompare(b.name)
                                    )}
                                    onMultiSelectChange={handleMultiSelectChange}
                                    selectedRuleFilters={additionalFilteredData}
                                    selectedRuleCategory={selectedRuleCategory}
                                    selectedMenuItems={selectedMenuItems}
                                    removeAddedRulesFilter={removeAddedRulesFilter}
                                />
                            );
                        })}
                </ProductCollectionRulesContainer>
            </AutomatedCollectionFilterField>
            {additionalFilter.length !== filteredCollectionRules.length && (
                <AutomatedCollectionFilterField>
                    <AdditionalFilterContainer>
                        <Box>
                            <Button
                                disabled={isAdditionalFilterDisabled}
                                label="Additional Filter"
                                iconName="Add"
                                onClick={() => addAdditionalFilter('')}
                                variant="text"
                            />
                        </Box>
                    </AdditionalFilterContainer>
                </AutomatedCollectionFilterField>
            )}
        </>
    );
};

export default ProductCollectionRules;
