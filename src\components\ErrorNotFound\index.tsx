import React from 'react';
import {ErrorTemplate, Link} from '@treez-inc/component-library';
import {useNavigate} from 'react-router-dom';
import CenteredContent from '../CenteredContent';

const ErrorNotFound: React.FC = () => {
    const navigate = useNavigate();
    const navigateToDashboard = () => navigate('/');

    return (
        <CenteredContent testId="error-not-found">
            <ErrorTemplate
                title="This page doesn't exist!"
                body={
                    <>
                        <p>Let&apos;s try one of these instead:</p>
                        <p>
                            <Link
                                ariaLabel="link that navigates to Treez support center"
                                href="https://support.treez.io/hc/en-us"
                            >
                                Support Center{' '}
                            </Link>
                        </p>
                        <p>
                            <Link
                                ariaLabel="link that navigates to Treez demo booking page"
                                href="https://www.treez.io/request-demo"
                            >
                                Book a Demo
                            </Link>
                        </p>
                    </>
                }
                buttonProps={{
                    label: 'Home',
                    onClick: navigateToDashboard,
                }}
                testId="error-template"
            />
        </CenteredContent>
    );
};

export default ErrorNotFound;

// api 404
