const {merge} = require('webpack-merge');
const singleSpaDefaults = require('webpack-config-single-spa-react-ts');
const Dotenv = require('dotenv-webpack');
const fs = require('fs');

module.exports = (webpackConfigEnv, argv) => {
    const envFile = `./.env.${webpackConfigEnv.stage}`;
    const defaultConfig = singleSpaDefaults({
        orgName: 'treez',
        projectName: 'product-collection',
        webpackConfigEnv,
        argv,
    });

    return merge(defaultConfig, {
        // modify the webpack config however you'd like to by adding to this object
        plugins: [
            new Dotenv({
                path: envFile,
            }),
        ],
        module: {
            rules: [
                {
                    test: /\.(woff|woff2|eot|ttf)$/,
                    use: [{loader: 'file-loader'}],
                },
            ],
        },
        ...(webpackConfigEnv.stage === 'local' && {
            devServer: {
                https: {
                    key: fs.readFileSync('./.cert/key.pem'),
                    cert: fs.readFileSync('./.cert/cert.pem'),
                },
            },
        }),
    });
};
