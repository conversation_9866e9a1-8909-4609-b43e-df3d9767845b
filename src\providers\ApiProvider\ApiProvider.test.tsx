import React from 'react';
import '@testing-library/jest-dom';
import {render, waitFor} from '@testing-library/react';
import ApiProvider, {ApiContext} from '.';
import ApiService from '../../utils/apiService';

const testId = 'consumerComponent';
const TestComponent = () => <div data-testid={testId} />;

describe('<ApiProvider />', () => {
    const renderApiProviderComponent = (api: ApiService) => {
        const {queryByTestId} = render(
            <ApiProvider api={api}>
                <ApiContext.Consumer>
                    {(value) => (value?.api !== null ? <TestComponent /> : null)}
                </ApiContext.Consumer>
            </ApiProvider>
        );

        return {queryByTestId};
    };

    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    it('adds api to context', async () => {
        const mockApi = jest.fn() as unknown as ApiService;
        const {queryByTestId} = renderApiProviderComponent(mockApi);

        await waitFor(() => {
            expect(queryByTestId(testId)).toBeInTheDocument();
        });
    });
});
