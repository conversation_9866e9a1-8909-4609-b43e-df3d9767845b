import React, {useMemo, createContext} from 'react';
import {Snackbar} from '@treez-inc/component-library';
import {ISnackbarContext} from '../../interfaces/snackbar';
import useSnackbar from '../../hooks/useSnackbar';

export const SnackbarContext = createContext<ISnackbarContext | null>(null);

export interface ISnackbarProviderProps {
    children: React.ReactNode;
}

const SnackbarProvider: React.FC<ISnackbarProviderProps> = ({children}) => {
    const [setSnackbar, snackbarProps] = useSnackbar();

    const provider = useMemo(() => ({setSnackbar}), []);

    return (
        <SnackbarContext.Provider value={provider}>
            {children}
            <Snackbar {...snackbarProps} />
        </SnackbarContext.Provider>
    );
};

export default SnackbarProvider;
