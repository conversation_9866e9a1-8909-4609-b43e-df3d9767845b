import { IRule } from "./productCollection";

export interface CollectionItems {
    variantId: string;
    isExclude: boolean | false;
}
export interface CollectionFormData {
    collectionName: string;
}
export interface CollectionFormError {
    isError: boolean;
    message: string;
}

export interface AutomatedCollectionFormData {
    id?: string;
    collectionName: string;
    sync: boolean;
    rule: IRule | {};
    collectionItems: CollectionItems[];
}

export interface AutomatedCollectionDetails {
    id: string;
    name: string;
    organizationId: string;
    sync: boolean;
    rule: IRule | {}
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
    collectionItems: CollectionItems[] | [];
}