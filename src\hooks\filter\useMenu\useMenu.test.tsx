import {renderHook, act} from '@testing-library/react';
import useMenu from '.';

describe('useMenu', () => {

    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    test('should open the menu when handleClick is called', () => {
        const {result} = renderHook(() => useMenu());

        act(() => {
            result.current.handleClick({currentTarget: {} as any} as any);
        });

        expect(result.current.open).toBe(true);
    });

    test('should close the menu when handleClose is called', () => {
        const {result} = renderHook(() => useMenu());

        act(() => {
            result.current.handleClose();
        });

        expect(result.current.open).toBe(false);
    });

    test('should set the selectedIndex when handleClickMenuItem is called', () => {
        const {result} = renderHook(() => useMenu());

        act(() => {
            result.current.handleClickMenuItem({currentTarget: {} as any} as any, 1);
        });

        expect(result.current.selectedIndex).toBe(1);
    });

    test('should set the selectedIndex and close the menu when handleClickMenuItemClose is called', () => {
        const {result} = renderHook(() => useMenu());

        act(() => {
            result.current.handleClickMenuItemClose({currentTarget: {} as any} as any, 2);
        });

        expect(result.current.selectedIndex).toBe(2);
        expect(result.current.open).toBe(false);
    });
});
