import useBasePriceLabel from '.';

describe('useBasePriceLabel()', () => {
    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    it('returns "-" for empty variant array', () => {
        const result = useBasePriceLabel([]);
        expect(result).toBe('-');
    });

    it('returns formatted price for a single variant', () => {
        const variants = [
            {
                id: 'eef22692-3a97-4b3f-89c2-08bba6dd3ce8',
                name: 'Variant A',
                isExclude: false,
                defaultPrices: {
                    base: 100,
                },
                sku: 'test',
                unitCount: 1,
                amount: 1,
                uom: 'Pack',
                merchandiseSize: 'XL',
            },
        ];
        const result = useBasePriceLabel(variants);
        expect(result).toBe('$1.00');
    });

    it('returns formatted price range for multiple variants with different prices', () => {
        const variants = [
            {
                id: 'eef22692-3a97-4b3f-89c2-08bba6dd3ce8',
                name: 'Variant A',
                isExclude: false,
                defaultPrices: {
                    base: 100,
                },
                sku: 'test',
                unitCount: 1,
                amount: 1,
                uom: 'Pack',
                merchandiseSize: 'XL',
            },
            {
                id: 'eef22692-3a97-4b3f-89c2-08bba6dd3ce8',
                name: 'Variant B',
                isExclude: false,
                defaultPrices: {
                    base: 200,
                },
                sku: 'test',
                unitCount: 1,
                amount: 1,
                uom: 'Pack',
                merchandiseSize: 'XL',
            },
        ];
        const result = useBasePriceLabel(variants);
        expect(result).toBe('$1.00 - $2.00');
    });

    it('returns formatted price for multiple variants with the same price', () => {
        const variants = [
            {
                id: 'eef22692-3a97-4b3f-89c2-08bba6dd3ce8',
                name: 'Variant A',
                isExclude: false,
                defaultPrices: {
                    base: 150,
                },
                sku: 'test',
                unitCount: 1,
                amount: 1,
                uom: 'Pack',
                merchandiseSize: 'XL',
            },
            {
                id: 'eef22692-3a97-4b3f-89c2-08bba6dd3ce8',
                name: 'Variant B',
                isExclude: false,
                defaultPrices: {
                    base: 150,
                },
                sku: 'test',
                unitCount: 1,
                amount: 1,
                uom: 'Pack',
                merchandiseSize: 'XL',
            },
        ];
        const result = useBasePriceLabel(variants);
        expect(result).toBe('$1.50');
    });

    it('returns "-" for multiple variants with no default prices', () => {
        const variants = [
            {
                id: 'eef22692-3a97-4b3f-89c2-08bba6dd3ce8',
                name: 'Variant A',
                isExclude: false,
                defaultPrices: {},
                sku: 'test',
                unitCount: 1,
                amount: 1,
                uom: 'Pack',
                merchandiseSize: 'XL',
            },
            {
                id: 'eef22692-3a97-4b3f-89c2-08bba6dd3ce8',
                name: 'Variant B',
                isExclude: false,
                defaultPrices: {},
                sku: 'test',
                unitCount: 1,
                amount: 1,
                uom: 'Pack',
                merchandiseSize: 'XL',
            },
        ];
        const result = useBasePriceLabel(variants);
        expect(result).toBe('-');
    });
});
