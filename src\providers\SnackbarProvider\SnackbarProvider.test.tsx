import React from 'react';
import '@testing-library/jest-dom';
import {render, waitFor} from '@testing-library/react';
import SnackbarProvider, {SnackbarContext} from '.';

describe('SnackbarProvider component', () => {
    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    it('can add setSnackbar to context', async () => {
        const testId = 'child';
        const TestComponent = () => <div data-testid={testId} />;

        const {queryByTestId} = render(
            <SnackbarProvider>
                <SnackbarContext.Consumer>
                    {(value) => (value?.setSnackbar ? <TestComponent /> : null)}
                </SnackbarContext.Consumer>
            </SnackbarProvider>
        );

        await waitFor(() => {
            expect(queryByTestId(testId)).toBeInTheDocument();
        });
    });
});
