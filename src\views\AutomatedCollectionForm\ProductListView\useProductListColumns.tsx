import React from 'react';
import {
    GridColDef,
    GRID_CHECKBOX_SELECTION_COL_DEF,
    GridCellCheckboxRenderer,
    selectedIdsLookupSelector,
    GridRenderCellParams,
} from '@mui/x-data-grid-pro';
import {Box, Checkbox, Typography} from '@mui/material';
import {StaticChip} from '@treez-inc/component-library';
import ProductListTableHeader from './ProductListTableHeader';
import getBasePriceLabel from '../../../hooks/useBasePriceLabel';

export default function useProductListColumns(
    excludeSelectionCheckedRef: React.MutableRefObject<boolean>,
    handleHeaderCheckboxChange: (event: React.ChangeEvent<HTMLInputElement>) => void,
    headerChecked: boolean,
    addedExcludedVariantIds: () => void,
  ): Array<GridColDef> {
    const columns: Array<GridColDef> = [
        {
            ...GRID_CHECKBOX_SELECTION_COL_DEF,
            renderHeader: () => {
                const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
                    handleHeaderCheckboxChange(event);
                    addedExcludedVariantIds();
                };
              
                return (
                  <Box display="flex" alignItems="center" gap={1}>
                    <Checkbox
                        checked={headerChecked}
                        onChange={handleChange}
                    />
                  </Box>
                );
            },
            renderCell: (params: any) => {
                const {rowNode} = params;

                const handleChildRowClick = (event: React.MouseEvent | React.KeyboardEvent) => {
                    // eslint-disable-next-line no-param-reassign
                    excludeSelectionCheckedRef.current = true;
                    event.stopPropagation();
                };

                if (rowNode.type !== 'group'){
                    return <GridCellCheckboxRenderer {...params} onClick={handleChildRowClick} />;
                }

                const selectionLookup = selectedIdsLookupSelector(
                    params.api.state,
                    params.api.instanceId
                );

                const indeterminate =
                    rowNode.children?.some((child: any) => selectionLookup[child] === undefined) &&
                    rowNode.children?.some((child: any) => selectionLookup[child] !== undefined);

                const checked = rowNode.children?.every(
                    (child: any) => selectionLookup[child] !== undefined
                );

                const extraData: GridRenderCellParams & {
                    indeterminate?: boolean;
                    checked?: boolean;
                    disabled?: boolean;
                    onClick?: (e: MouseEvent) => void;
                } = {
                    ...params,
                    disabled: false,
                    onClick: (e) => {
                        if (rowNode.type === 'group') {
                            if (rowNode.children) {
                                // eslint-disable-next-line no-param-reassign
                                excludeSelectionCheckedRef.current = true;
                                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                                // @ts-ignore
                                params.api.selectRows(rowNode.children, indeterminate || !checked);
                                addedExcludedVariantIds();
                            }
                            e.preventDefault();
                        }
                    },
                    indeterminate,
                    checked,
                };

                return <GridCellCheckboxRenderer {...extraData} />;
            },
            sortable: false,
            flex: 0.25,
        },
        {
            renderHeader: () => ProductListTableHeader({name: 'Product'}),
            field: 'productName',
            flex: 1.2,
            renderCell: ({row}) => <Typography> {row.productName}</Typography>,
        },
        {
            renderHeader: () => ProductListTableHeader({name: 'SKU'}),
            field: 'sku',
            renderCell: ({row}) => <Typography>{row?.variants?.sku}</Typography>,
        },
        {
            renderHeader: () => ProductListTableHeader({name: 'Amount'}),
            field: 'allSizes',
            flex: 1,
            renderCell: (params) => (
                <Box sx={{display: 'flex', width: '100%'}}>
                    {params?.row?.formattedVariantSizes &&
                        params.row.formattedVariantSizes.map((item: string) =>  
                            item && (
                            <Box
                                key={`chip-${item}-${Math.random().toFixed(6)}`}
                                sx={{marginRight: '4px'}}
                            >
                                <StaticChip
                                    variant="filled"
                                    testId="multiplestore-chip"
                                    // eslint-disable-next-line no-nested-ternary
                                    label={item}
                                />
                            </Box>
                        ))}
                </Box>
            ),
        },
        {
            renderHeader: () => ProductListTableHeader({name: 'Brand'}),
            field: 'brandName',
            flex: 0.5,
            renderCell: ({row}) => <Typography>{row.brandName}</Typography>,
        },
        {
            renderHeader: () => ProductListTableHeader({name: 'Category'}),
            field: 'productCategoryName',
            flex: 0.5,
            renderCell: ({row}) => <Typography>{row.productCategoryName}</Typography>,
        },
        {
            renderHeader: () => ProductListTableHeader({name: 'Subcategory'}),
            field: 'productSubCategoryName',
            flex: 0.5,
            renderCell: ({row}) => (
                <Typography>{row.productSubCategoryName?.split(' - ').pop()}</Typography>
            ),
        },
        {
            renderHeader: () => ProductListTableHeader({name: 'Price'}),
            field: 'basePrice',
            flex: 1,
            sortable: false,
            renderCell: ({row}) => {
                const variants = Array.isArray(row.variants) ? row.variants : [row.variants];
                const basePriceRange = getBasePriceLabel(variants);
                return <Typography>{basePriceRange}</Typography>;
            },
        },
    ];

    return columns;
}
