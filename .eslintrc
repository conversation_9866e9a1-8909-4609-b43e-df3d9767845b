{"parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "ignorePatterns": ["/*.*", "cypress/*", "coverage/*"], "overrides": [{"files": ["*.ts", "*.tsx"]}], "extends": ["airbnb", "airbnb-typescript", "prettier"], "parserOptions": {"project": ["./tsconfig.json"]}, "rules": {"import/no-unresolved": "off", "import/no-extraneous-dependencies": "off", "import/order": "error", "import/prefer-default-export": "off", "react/function-component-definition": ["error", {"namedComponents": ["function-declaration", "arrow-function"], "unnamedComponents": "arrow-function"}], "no-param-reassign": ["error", {"props": false}], "react/jsx-props-no-spreading": "off", "react/require-default-props": "off", "react/jsx-no-useless-fragment": "off"}}