import React from 'react';
import '@testing-library/jest-dom';
import {fireEvent, render, waitFor} from '@testing-library/react';
import {TreezThemeProvider} from '@treez-inc/component-library';
import SnackbarProvider from '../../../providers/SnackbarProvider';
import AddAutomatedCollectionName from '.';

describe('<AddAutomatedCollectionName/>', () => {
    it('renders properly and buttons trigger correct actions', async () => {
        const formData = {collectionName: '', sync: true, rule: [], collectionItems: []};
        const setFormData = jest.fn();
        const setFormError = jest.fn();
        const setIsUpdatePendingMock = jest.fn();

        const {getByTestId} = render(
            <TreezThemeProvider>
                <SnackbarProvider>
                    <AddAutomatedCollectionName
                        formData={formData}
                        setFormData={setFormData}
                        formError={{collectionName: {isError: false, message: ''}}}
                        setFormError={setFormError}
                        setIsUpdatePending={setIsUpdatePendingMock}
                    />
                </SnackbarProvider>
            </TreezThemeProvider>
        );

        const inputDiv = getByTestId('automated-collection-name-field');
        const input = inputDiv.querySelector('input') as HTMLInputElement;
        fireEvent.change(input, {target: {value: 'New Collection Name'}});

        await waitFor(() => {
            expect(setFormData).toHaveBeenCalledTimes(1);
        });
    });
});
