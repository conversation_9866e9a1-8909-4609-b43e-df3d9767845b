import {Box, styled} from '@mui/material/';
import {Button} from '@treez-inc/component-library';
import convertPxToRem from '@treez-inc/component-library/dist/util-functions/convertPxToRem';
import React, {useEffect, useState} from 'react';
import {IFilterCheckbox, IFilterComponent, IFilterState} from '../../interfaces/collectionFilter';
import CollectionStatus from '../../interfaces/collectionStatus.enum';
import CollectionStatusFilterComponent from '../CollectionStatusFilter';

const StyledFilterContainer = styled(Box)(() => ({
    paddingLeft: convertPxToRem(12),
    // TODO: uncomment display when this filter is re enabled
    // display: 'flex',
    display: 'none',
    alignItems: 'center',
    justifyContent: 'center',
    gap: convertPxToRem(8),
    zIndex: 1,
}));

const FilterComponent: React.FC<IFilterComponent> = ({onChange}) => {
    const [collectionStatusFilterState, setCollectionStatusFilterState] = useState<IFilterState>({
        status: [],
    });

    const [collectionStatusList, setCollectionStatusList] = useState([
        {
            key: CollectionStatus.ACTIVE,
            value: CollectionStatus.ACTIVE,
            label: 'Active',
            checked: true,
        },
        {
            key: CollectionStatus.INACTIVE,
            value: CollectionStatus.INACTIVE,
            label: 'Inactive',
            checked: false,
        },
    ]);

    const isAnyFilterActive = () => collectionStatusFilterState?.status?.length > 0;

    const [filterActive, setFilterActive] = useState(false);

    useEffect(() => {
        const updatedFilterValues = {
            ...collectionStatusFilterState,
        };

        onChange(updatedFilterValues);
        setFilterActive(isAnyFilterActive());
    }, [collectionStatusFilterState, collectionStatusList]);

    const onCollectionStatusFilter = (filterItem: string, cb: IFilterCheckbox[]) => {
        setCollectionStatusFilterState((prv: IFilterState) => ({
            ...prv,
            status: cb?.filter((ob) => ob.checked).map((ob) => ob?.value),
        }));
    };

    const handleClearAll = () => {
        setCollectionStatusFilterState({
            status: [],
        });

        const updatedCollectionStatusList = collectionStatusList.map((item) => ({
            ...item,
            checked: false,
        }));

        setCollectionStatusList(updatedCollectionStatusList);
        setFilterActive(false);
    };

    useEffect(() => {
        setFilterActive(isAnyFilterActive());
    }, [collectionStatusList]);

    return (
        <StyledFilterContainer>
            <CollectionStatusFilterComponent
                filterItem="collection-status"
                label="Status"
                chipId="collection-dropdown-chip"
                menuId="collection-checkbox"
                values={collectionStatusList}
                onChange={onCollectionStatusFilter}
                badgeContent={collectionStatusFilterState?.status?.length}
                status={collectionStatusFilterState.status}
            />
            {filterActive && (
                <Button
                    iconName="Close"
                    label="Clear all"
                    onClick={handleClearAll}
                    testId="clear-all-filters-button"
                    variant="text"
                    disabled={!filterActive}
                />
            )}
        </StyledFilterContainer>
    );
};

export default FilterComponent;
