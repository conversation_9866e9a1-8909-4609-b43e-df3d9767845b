import IProductCollection, {CollectionCreateDTO} from '../src/interfaces/productCollection';
import {TUser} from './support/types';

declare global {
    namespace Cypress {
        interface Chainable {
            /**
             * Custom command to login through the authentication API
             * @example cy.login(username, password)
             */
            login(username: string, password: string): Chainable<Element>;

            /**
             * Custom command to login as a particular user through the authentication API.
             *
             * To add more user types, include their credentials in `cypress/support/cypressEnv.ts` and update `TUser` type to add the new key.
             * @example cy.loginAs("admin")
             */
            loginAs(userType: TUser): Chainable<Element>;

            /**
             * Custom command to create a tag through the product-collection API
             * @example cy.createCollection(payload)
             */
            createCollection(payload?: CollectionCreateDTO): Chainable<IProductCollection>;
        }
    }
}

export {};
