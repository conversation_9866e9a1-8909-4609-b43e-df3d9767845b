import {SSMClient} from '@aws-sdk/client-ssm';
if (
    process.env.AWS_ACCESS_KEY_ID === undefined ||
    process.env.AWS_SECRET_ACCESS_KEY === undefined
) {
    throw new Error('AWS credentials must be defined');
}

const AWS_CONFIG = {
    region: 'us-west-2',
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID as string,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY as string,
        sessionToken: process.env.AWS_SESSION_TOKEN,
    },
};

const ssmClient = new SSMClient(AWS_CONFIG);

export default ssmClient;
