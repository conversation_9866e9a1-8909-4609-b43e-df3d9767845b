import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { TreezThemeProvider } from '@treez-inc/component-library';
import { createMemoryHistory } from 'history';
import { MemoryRouter } from 'react-router-dom';
import ProductCollection from '.';
import { productCollectionListData } from '../../test/testData';
import ApiService from '../../utils/apiService';
import SnackbarProvider from '../../providers/SnackbarProvider';
import { MFEPermissions } from '../../interfaces/permissions';

const testIdCollectionPageLayout = 'collections-page-layout';
const testIdCollectionsGridView = 'collections-grid-layout';
const testIdColumnHeader = 'columnheader';
const testIdRows = 'rows';

jest.mock('../../hooks/useProductCollections');
jest.mock('../../constants/routes');
jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useParams: () => ({
        id: '123',
    }),
}));
jest.mock('../../hooks/useApiContext');
jest.mock('../../components/RouteError', () => () => <div data-testid="mock-route-error" />);
jest.mock('../../hooks/usePermissionsContext');
const mockUserPermissionsContext = require('../../hooks/usePermissionsContext').default;

const orgId = '4a873b6d-e350-4955-a254-bb37b8bc81f6';
const permission = {
    permissions: {
        [MFEPermissions.VIEW_PRODUCT_COLLECTIONS]: true,
        [MFEPermissions.MANAGE_PRODUCT_COLLECTIONS]: true,
    },
    orgId,
};

const mockUseProductCollections = require('../../hooks/useProductCollections').default;

const mockUseApiContext = require('../../hooks/useApiContext').default;

const validTokens = {
    accessToken: 'accessToken',
    expiresIn: 300,
    refreshToken: 'refreshToken',
    idToken: 'idToken',
};
const clearTokens = jest.fn();
const getTokens = () => validTokens;
const redirectToLogin = jest.fn();
const apiService = new ApiService(getTokens, clearTokens, redirectToLogin);

describe('<ProductCollection />', () => {
    beforeEach(() => {
        mockUseProductCollections.mockReturnValue({
            loading: false,
            error: null,
            data: productCollectionListData,
        });
        mockUseApiContext.mockImplementation(() => apiService);
        mockUserPermissionsContext.mockImplementation(() => permission);
    });

    const renderProductCollectionView = () => {
        const history = createMemoryHistory();
        const { queryByTestId, queryAllByRole } = render(
            <MemoryRouter>
                <TreezThemeProvider>
                    <SnackbarProvider>
                        <ProductCollection />
                    </SnackbarProvider>
                </TreezThemeProvider>
            </MemoryRouter>
        );

        const productCollectionsView = queryByTestId(testIdCollectionPageLayout);
        const productCollectionsListWrapper = queryByTestId(testIdCollectionsGridView);
        const columnHeaders = queryAllByRole(testIdColumnHeader);
        const rows = queryAllByRole(testIdRows);
        const productCollectionsCount = queryByTestId('collections-count');
        const collectionAddButton = queryByTestId('add-product-collection-button');
        const collectionManualAddButton = queryByTestId('add-manual-product-collection-button');
        const getAddCollectionModal = () => queryByTestId('add-collection-modal');
        const getManualAddCollectionModal = () => queryByTestId("add-manual-collection-modal");
        const getCancelButton = () => queryByTestId('secondaryButton-add-collection-modal');
        const getCreateCollectionButton = () => queryByTestId('primaryButton-add-collection-modal');

        return {
            productCollectionsView,
            productCollectionsListWrapper,
            columnHeaders,
            rows,
            productCollectionsCount,
            history,
            collectionAddButton,
            collectionManualAddButton,
            getAddCollectionModal,
            getManualAddCollectionModal,
            getCancelButton,
            getCreateCollectionButton,
        };
    };

    it('should render with correct text and styles', () => {
        const { productCollectionsView, productCollectionsListWrapper } =
            renderProductCollectionView();

        expect(productCollectionsView).toBeInTheDocument();
        expect(productCollectionsListWrapper).toBeInTheDocument();
    });

    it('renders Product Collection List View with correct column headers', () => {
        const { productCollectionsView, columnHeaders } = renderProductCollectionView();

        expect(productCollectionsView).toBeInTheDocument();
        // TODO: uncomment the assert when this column is re enabled
        // expect(columnHeaders).toHaveLength(5);
        expect(columnHeaders).toHaveLength(4);
        expect(columnHeaders[1]).toHaveTextContent('Collection');
        expect(columnHeaders[2]).toHaveTextContent('Last update');
        expect(columnHeaders[3]).toHaveTextContent('Type');
        // TODO: uncomment this when this column is re enabled
        // expect(columnHeaders[4]).toHaveTextContent('Status');
        expect(mockUseProductCollections).toHaveBeenCalledTimes(1);
    });

    it('renders data grid with active status formatting', () => {
        mockUseProductCollections.mockReturnValue({
            loading: false,
            error: null,
            data: productCollectionListData,
        });
        renderProductCollectionView();
        const rows = screen.getAllByRole('row');
        expect(rows).toHaveLength(4);
        // TODO: uncomment this when this column is re enabled
        // expect(rows[1]).toHaveTextContent('Active');
    });


    it('should open model on clicking Add collection button and able to close it', () => {
        const { collectionAddButton, getAddCollectionModal, getCancelButton } =
            renderProductCollectionView();

        expect(collectionAddButton).toHaveTextContent('Add Collection');
        if (collectionAddButton) {
            fireEvent.click(collectionAddButton);
        }
        expect(getAddCollectionModal()).toBeInTheDocument();
        const cancelBtn = getCancelButton();
        if (cancelBtn) {
            fireEvent.click(cancelBtn);
        }
        expect(getAddCollectionModal()).not.toBeInTheDocument();
    });

    it('should open model from clicking on create in Add collection modal and able to close it', async () => {
        const { collectionAddButton, getAddCollectionModal, getCreateCollectionButton, getCancelButton } =
            renderProductCollectionView();

        expect(collectionAddButton).toHaveTextContent('Add Collection');
        if (collectionAddButton) {
            fireEvent.click(collectionAddButton);
            const createCollectionButton = getCreateCollectionButton();
            if (createCollectionButton) {
                fireEvent.click(createCollectionButton);
                const cancelBtn = getCancelButton();
                if (cancelBtn) {
                    fireEvent.click(cancelBtn);
                }
                expect(getAddCollectionModal()).not.toBeInTheDocument();
            }
        }


    });

    it('renders RouteError when useProductCollection returns an error', () => {
        const mockError = new Error('Data fetching error');
        mockUseProductCollections.mockReturnValue({
            loading: false,
            error: mockError,
            data: null,
        });

        renderProductCollectionView();
        expect(screen.getByTestId('mock-route-error')).toBeInTheDocument();
        expect(screen.queryByTestId('collection-details-page-layout')).not.toBeInTheDocument();
    });
});
