import {useReducer} from 'react';
import {NullableType} from 'joi';
import {GridFilterModel, GridSortModel} from '@mui/x-data-grid-pro';
import {ProductDto} from '../../../interfaces/dto/product';
import {ProductSearchQueryProps} from '../../../queries/useProductSearchQuery';

enum ProductListReducerActionType {
    UPDATE_FILTER_MODEL,
    UPDATE_SORT_MODEL,
    UPDATE_PAGINATION_MODEL,
}

interface ProductListDefaultStateProps {
    isGlobalLoading: boolean;
    isDrawerOpen: boolean;
    isMergeDrawerOpen: boolean;
    drawerProduct: NullableType<ProductDto>;
    filterModel: GridFilterModel;
    sortModel: GridSortModel;
    productFilterModel: {subCategory: string[]; status: string[]};
    paginationModel: {page: number; pageSize: number};
}

const ProductListDefaultState: ProductListDefaultStateProps = {
    isGlobalLoading: false,
    isDrawerOpen: false,
    isMergeDrawerOpen: false,
    drawerProduct: null,
    filterModel: {items: []},
    sortModel: [],
    productFilterModel: {
        subCategory: [],
        status: ['active'],
    },
    paginationModel: {page: 0, pageSize: 20},
};

interface ProductListReducerAction {
    type: ProductListReducerActionType;
    payload: any;
}

const ProductListReducer = (
    state: ProductListDefaultStateProps,
    action: ProductListReducerAction
) => {
    const stateMap = {
        [ProductListReducerActionType.UPDATE_FILTER_MODEL]: {filterModel: action.payload},
        [ProductListReducerActionType.UPDATE_SORT_MODEL]: {sortModel: action.payload},
        [ProductListReducerActionType.UPDATE_PAGINATION_MODEL]: {paginationModel: action.payload},
    };
    return {...state, ...(stateMap[action.type] || {})};
};

export default function useProductListReducer(filters: any) {
    const [{filterModel, sortModel, productFilterModel, paginationModel}, dispatch] = useReducer(
        ProductListReducer,
        ProductListDefaultState
    );

    const setSortModel = (payload: any) => {
        dispatch({type: ProductListReducerActionType.UPDATE_SORT_MODEL, payload});
    };

    const setFilterModel = (payload: GridFilterModel) => {
        dispatch({type: ProductListReducerActionType.UPDATE_FILTER_MODEL, payload});
    };

    const setPaginationModel = (payload: {page: number; pageSize: number}) => {
        dispatch({type: ProductListReducerActionType.UPDATE_PAGINATION_MODEL, payload});
    };

    const searchTerm = filterModel?.quickFilterValues?.join(' ') || undefined;

    // Take existing filters and replace the status filter if there's a new one from productFilterModel
    const mergedFilters = {
        ...filters,
        ...(productFilterModel?.status?.length ? {status: productFilterModel.status} : {}),
    };

    const searchProps: ProductSearchQueryProps = {
        search: searchTerm,
        paging: {pageNumber: paginationModel.page, size: paginationModel.pageSize},
        sort:
            sortModel.length > 0
                ? {key: sortModel[0].field, isAscending: sortModel[0].sort === 'asc'}
                : {key: 'lastUpdated', isAscending: false},
        filters: mergedFilters,
    };

    return {
        setSortModel,
        setFilterModel,
        setPaginationModel,
        sortModel,
        filterModel,
        searchProps,
    };
}
