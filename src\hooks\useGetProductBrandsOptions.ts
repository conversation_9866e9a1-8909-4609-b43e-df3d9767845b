import { useQuery } from 'react-query';
import { UseQueryOptions } from 'react-query/types/react/types';
import { getData } from '../api/genericAccessor';
import { BrandDto } from '../interfaces/brand';
import Entities from '../interfaces/entities.enum';
import { PRODUCT_CONTROL_API_URL } from '../constants/apiEndPoints';

const useGetProductBrandsOptions  = (options?: UseQueryOptions): { data: BrandDto[]; isLoading: boolean } => {
    const { data, isLoading } = useQuery({
        queryKey: ['brand'],
        queryFn: async () => {
            const result: BrandDto[] = await getData(PRODUCT_CONTROL_API_URL, Entities.BRAND, {});
            return result;
        },
        ...options,
    });
    return { data: data as BrandDto[], isLoading };
};

export default useGetProductBrandsOptions;