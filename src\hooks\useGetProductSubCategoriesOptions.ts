import { useQuery } from 'react-query';
import { UseQueryOptions } from 'react-query/types/react/types';
import { getData } from '../api/genericAccessor';
import Entities from '../interfaces/entities.enum';
import { ProductSubCategoryDto } from '../interfaces/productSubCategory';
import { PRODUCT_CONTROL_API_URL } from '../constants/apiEndPoints';

const useGetProductSubCategoriesOptions  = (options?: UseQueryOptions): { data: ProductSubCategoryDto[]; isLoading: boolean } => {
    const { data, isLoading } = useQuery({
        queryKey: ['product-subcategory'],
        queryFn: async () => {
            const result: ProductSubCategoryDto[] = await getData(PRODUCT_CONTROL_API_URL, Entities.PRODUCT_SUB_CATEGORY, {});
            return result;
        },
        ...options,
    });
    return { data: data as ProductSubCategoryDto[], isLoading };
};

export default useGetProductSubCategoriesOptions;