import ApiService from '.';

jest.mock('../apiInterceptor');

const api = require('../apiInterceptor').default;

const validTokens = {
    accessToken: 'accessToken',
    expiresIn: 300,
    refreshToken: 'refreshToken',
    idToken: 'idToken',
};

describe('Api Service', () => {
    beforeEach(() => {
        api.mockImplementation(() => ({
            get: jest.fn(),
            post: jest.fn(),
            put: jest.fn(),
            delete: jest.fn(),
        }));
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    it('should perform get request with given parameters ', async () => {
        const getTokens = () => validTokens;
        const refreshTokens = async () => validTokens;

        const headers = {
            headers: {
                Authorization: 'accessToken',
                retry: '0',
            },
        };
        const apiService = new ApiService(getTokens, refreshTokens, () => {});
        await apiService.get('mockurl');
        expect(apiService.api.get).toBeCalledWith('mockurl', headers);
        expect(apiService.api.get).toBeCalledTimes(1);
    });

    it('should perform post request with given parameters ', async () => {
        const getTokens = () => validTokens;
        const refreshTokens = async () => validTokens;

        const headers = {
            headers: {
                Authorization: 'accessToken',
                retry: '0',
            },
        };
        const apiService = new ApiService(getTokens, refreshTokens, () => {});
        await apiService.post('mockurl', {});
        expect(apiService.api.post).toBeCalledWith('mockurl', {}, headers);
        expect(apiService.api.post).toBeCalledTimes(1);
    });

    it('should perform put request with given parameters ', async () => {
        const getTokens = () => validTokens;
        const refreshTokens = async () => validTokens;

        const headers = {
            headers: {
                Authorization: 'accessToken',
                retry: '0',
            },
        };
        const apiService = new ApiService(getTokens, refreshTokens, () => {});
        await apiService.put('mockurl', {});
        expect(apiService.api.put).toBeCalledWith('mockurl', {}, headers);
        expect(apiService.api.put).toBeCalledTimes(1);
    });

    it('should perform delete request with given parameters ', async () => {
        const getTokens = () => validTokens;
        const refreshTokens = async () => validTokens;

        const headers = {
            headers: {
                Authorization: 'accessToken',
                retry: '0',
            },
        };
        const apiService = new ApiService(getTokens, refreshTokens, () => {});
        await apiService.delete('mockurl');
        expect(apiService.api.delete).toBeCalledWith('mockurl', headers);
        expect(apiService.api.delete).toBeCalledTimes(1);
    });
});
