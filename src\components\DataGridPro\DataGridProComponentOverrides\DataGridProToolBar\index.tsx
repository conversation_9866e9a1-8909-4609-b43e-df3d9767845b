import React from 'react';
import { Box, styled, Typography } from '@mui/material';
import { convertPxToRem } from '@treez-inc/component-library';
import { GridToolbarQuickFilter, GridToolbarQuickFilterProps } from '@mui/x-data-grid-pro';


interface DataGridProToolBarProps {
    quickFilterValue?: string,
    // removeSearchFilter removes the search input field from DataGrid when there is no data to search for.
    removeSearchFilter?: boolean;
    // numberOfExcluded is the number of excluded rows ( The rows that are checked ) in the DataGrid.
    numberOfExcluded?: number;
    quickFilterProps?: GridToolbarQuickFilterProps;
}

const StyledToolbar = styled('div', {
    shouldForwardProp: (prop) => prop !== 'showBulkActionBar',
})<{ showBulkActionBar: boolean }>(({ showBulkActionBar, theme }) => ({
    display: 'flex',
    justifyContent: showBulkActionBar ? 'space-between' : 'flex-end',
    gap: convertPxToRem(12),
    padding: convertPxToRem(12),

    '& .MuiInput-root:hover:not(.Mui-disabled):before': {
        borderBottom: 'none',
    },
    '& .data-grid-pro-search-field': {
        flexShrink: 0,
        minWidth: convertPxToRem(257),
        marginLeft: convertPxToRem(12),
        display: 'flex',
        justifyContent: 'center',
        backgroundColor: theme.palette.treezGrey[1],
        padding: `0 ${convertPxToRem(12)}`,
        borderRadius: convertPxToRem(15),
        span: {
            marginRight: convertPxToRem(8),
        },
        boxSizing: 'border-box',
        height: convertPxToRem(40),
        '.MuiInput-underline:before': {
            borderBottom: 'none',
        },
        '.MuiInput-root': {
            '&:after': {
                borderBottom: 'none',
            },
            '&:hover': {
                borderBottom: 'none',
            },
        },
    },
}));

// TODO: add custom filtering components & custom search bar to this component
const DataGridProToolBar = (props: DataGridProToolBarProps | any = {}) => {
    const { 
        quickFilterValue, 
        numberOfExcluded, 
        removeSearchFilter, 
        bulkActionBarProps, 
        quickFilterProps, 
        ...restProps 
    } = props;

    const showSearchFilter = !removeSearchFilter || quickFilterValue.length > 0;

    return (
        <StyledToolbar showBulkActionBar={Boolean(bulkActionBarProps)} data-testid="data-grid-pro-toolbar">
            <Box display="flex" justifyContent="space-between" alignItems="center" width="100%">
                <Typography variant="subtitle2">{numberOfExcluded} Excluded</Typography>

                {showSearchFilter && (
                    <GridToolbarQuickFilter
                        {...restProps}
                        className="data-grid-pro-search-field"
                        debounceMs={quickFilterProps?.debounceMs ?? 500}
                    />)
                }
            </Box>
        </StyledToolbar>
    );
};

export default DataGridProToolBar;
