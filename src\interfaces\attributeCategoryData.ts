
export interface Base {
    createdAt: Date;
    updatedAt?: Date;
    deletedAt?: Date;
}

export interface AttributeDto extends Base {
    id: string;
    name: string;
    attributeCategoryId?: string;
}

export interface AttributeCategoryData extends Base {
    id: string;
    name: string;
    organizationId: string;
    attributes: AttributeDto[];
}

export interface CollectionRuleOptions {
    id: string;
    name: string;
}

export interface AutomatedCollectionRulesForm {
    name: string;
    ruleType: string;
    inputType: string;
    options: CollectionRuleOptions[] | [];
    custom: boolean;
    active: boolean;
}