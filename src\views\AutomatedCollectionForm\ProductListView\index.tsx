import React, {useState, useEffect, useMemo} from 'react';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, Switch} from '@treez-inc/component-library';
import {Box, ButtonProps, styled} from '@mui/material';
import {
    DataGridProProps,
    GridRowId,
    GridRowSelectionModel,
    GridTreeNodeWithRender,
    useGridApiContext,
    useGridApiRef,
} from '@mui/x-data-grid-pro';
import {nanoid} from 'nanoid';
import useProductListColumns from './useProductListColumns';
import useProductListReducer from './ProductListReducer';
import {DataGridPro} from '../../../components/DataGridPro';
import {DEBOUNCE_TIME, statusMap} from '../../../utils/constants';
import {VariantDto} from '../../../interfaces/dto/variant';
import {ProductSearchResponse} from '../../../interfaces/dto/product';
import useSnackbarContext from '../../../hooks/useSnackbarContext';
import useDebounce from '../../../hooks/useDebounce';
import useProductSearchQuery from '../../../queries/useProductSearchQuery';
import {addUniqueIds, removeMatchingIds} from '../../../utils/filterUtil';
import {getVariantAmountLabel} from '../../../utils/variantUtils';
import collectionUtil from '../../../utils/collectionForm';
import {CollectionItems} from '../../../interfaces/collectionForm';
import './ProductListView.style.css';

const mapData = (value: string, columnData: any) => columnData[value?.toLowerCase()] || value;

export const formatVariantSize = (categoryName: string, variant: VariantDto): string => {
    const formattedSize: string = getVariantAmountLabel(variant);
    return formattedSize;
};

const Container = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
}));

const StyledErrorContainer = styled(Box)(() => ({
    display: 'flex',
    height: '100%',
    justifyContent: 'center',
    width: '100%',
    alignItems: 'center',
}));

const CustomGroupGridTreeContainer = styled(Box)(() => ({
    display: 'flex',
}));

const NoProductsOverlay = () => (
    <StyledErrorContainer>No product(s) found for the applied filter(s)</StyledErrorContainer>
);

function CustomGridTreeDataGroupingCellActionMenu(props: {
    id: GridRowId;
    field: string;
    row: ProductSearchResponse;
    rowNode: GridTreeNodeWithRender;
}) {
    const {id, field, rowNode, row} = props;

    const apiRef = useGridApiContext();
    const [isExpanded, setExpanded] = useState(false);

    const handleClick: ButtonProps['onClick'] = (event) => {
        if (rowNode.type !== 'group') {
            return;
        }
        apiRef.current.setRowChildrenExpansion(id, !rowNode.childrenExpanded);
        apiRef.current.setCellFocus(id, field);
        setExpanded(!rowNode.childrenExpanded);
        event.stopPropagation();
    };

    useEffect(() => {
        if (rowNode.type !== 'group') {
            return;
        }

        if (isExpanded !== rowNode.childrenExpanded) {
            apiRef.current.setRowChildrenExpansion(id, !rowNode.childrenExpanded);
            apiRef.current.setCellFocus(id, field);
        }
    }, [rowNode]);

    return (
        <CustomGroupGridTreeContainer key={`grid-tree-container-${id}`}>
            {!row.isChild && (
                <IconButton
                    iconName={isExpanded ? 'ChevronUp' : 'ChevronRight'}
                    onClick={handleClick}
                    size="small"
                    testId="expanddiscountrow-button"
                    variant="secondary"
                />
            )}
        </CustomGroupGridTreeContainer>
    );
}

export default function ProductList({
    selectedCategory,
    selectedRules,
    excludedVariantIds,
    setExcludedVariantIds,
    excludeSelectionCheckedRef,
    collectionDetailsData,
}: {
    selectedCategory: string;
    selectedRules: any;
    excludedVariantIds: any;
    setExcludedVariantIds: any;
    excludeSelectionCheckedRef: React.MutableRefObject<boolean>;
    collectionDetailsData: any;
}) {
    const [responseStatus, setResponseStatus] = useState<number | null>(null);
    const [selectionModel, setSelectionModel] = useState<GridRowSelectionModel>([]);
    const [productInfoDataRows, setProductInfoDataRows] = useState<ProductSearchResponse[]>([]);
    const {setSnackbar} = useSnackbarContext();
    const [isExcludedVariantsChecked, setExcludedVariantsChecked] = useState<boolean | undefined>(
        false
    );
    const [headerChecked, setHeaderChecked] = React.useState(false);
    const [manualChange, setManualChange] = useState(false);
    const hasMounted = React.useRef(false);
    const [removeSearchFilterAndPagination, setRemoveSearchFilterAndPagination] = useState(false);

    // Create the filters based on what the user selected. Only updates when one of the inputs changes.
    const filters = useMemo(() => {
        const baseFilters: Record<string, any> = {
            ...selectedRules,
        };

        if (selectedCategory) {
            baseFilters.category = [selectedCategory];
        }

        if (isExcludedVariantsChecked) {
            baseFilters.variantIds = excludedVariantIds;
        }

        return baseFilters;
    }, [selectedRules, selectedCategory, isExcludedVariantsChecked, excludedVariantIds]);

    // Grabs all variant id's for the current page
    const {setSortModel, setFilterModel, sortModel, filterModel, searchProps, setPaginationModel} =
        useProductListReducer(filters);

    const searchTerm = searchProps.search;
    const debouncedSearchTerm = useDebounce(searchTerm, DEBOUNCE_TIME);
    searchProps.search = debouncedSearchTerm;

    if (searchProps.filters?.category && searchProps.filters?.category[0] === 'global') {
        delete searchProps.filters?.category;
    }

    useEffect(() => {}, [debouncedSearchTerm]);

    const productSearchQuery = useProductSearchQuery(
        {
            ...searchProps,
        },
        {
            initialData: [],
            keepPreviousData: true,
            refetchOnWindowFocus: false,
            placeholderData: [],
        }
    );

    const {data: productData, isError, isLoading, isFetching, error} = productSearchQuery;

    useEffect(() => {
        if (isError) {
            setSnackbar({
                message: 'There was an error retrieving products.',
                severity: 'error',
                iconName: 'Error',
            });
        }
        setResponseStatus((error as any)?.response?.status);
    }, [productSearchQuery]);

    // Triggered when the header checkbox is manually clicked by the user
    const handleHeaderCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setManualChange(true);
        setHeaderChecked(event.target.checked); // Update the headerChecked state
    };

    const addedExcludedVariantIds = () => {
        const updatedCollectionItems = collectionUtil.mergeExcludedVariants(
            collectionDetailsData?.collectionItems,
            excludedVariantIds
        );

        const excludedCollectionItems = updatedCollectionItems
            .filter((item: CollectionItems) => item.isExclude === true)
            .map((item: CollectionItems) => item.variantId);
        setExcludedVariantIds(excludedCollectionItems);
    };

    const apiRef = useGridApiRef();
    const columns = useProductListColumns(
        excludeSelectionCheckedRef,
        handleHeaderCheckboxChange,
        headerChecked,
        addedExcludedVariantIds
    );

    useEffect(() => {
        if ((productData as any)?.data && Array.isArray((productData as any).data)) {
            // eslint-disable-next-line no-param-reassign
            excludeSelectionCheckedRef.current = false;

            const newData: any[] = [];

            const updatedExcludedVariantIds: any[] = [...excludedVariantIds];
            const excludedProductIds: string[] = [];

            (productData as any).data.forEach((item: any) => {
                const product = item;
                product.variants = item?.variants?.filter(
                    (variant: VariantDto) =>
                        variant.status && item.status && variant.status === item.status
                );
                const formattedVariantSizes = product?.variants?.map((variant: VariantDto) =>
                    formatVariantSize(product?.productCategoryName, variant)
                );
                const status = mapData(product.status, statusMap);

                if (product.variants && product.variants.length > 0) {
                    newData.push({
                        ...product,
                        formattedVariantSizes,
                        status,
                        hierarchy: [`disc-${product.productId}`],
                        id: product.productId,
                        isChild: false,
                    });

                    product.variants.forEach((variant: VariantDto) => {
                        if (variant) {
                            const key = `store-ux-${nanoid()}`;
                            newData.push({
                                ...product,
                                formattedVariantSizes: [
                                    formatVariantSize(product?.productCategoryName, variant),
                                ],
                                status: mapData(variant.status, statusMap),
                                variants: [variant],
                                id: variant.id as string,
                                lastUpdated: variant.updatedAt || '',
                                hierarchy: [`disc-${product.productId}`, key],
                                isChild: true,
                                productName: variant?.name,
                            });

                            if (
                                updatedExcludedVariantIds.indexOf(variant.id) !== -1 &&
                                updatedExcludedVariantIds.indexOf(product.productId) === -1
                            ) {
                                updatedExcludedVariantIds.push(product.productId);
                                excludedProductIds.push(product.productId);
                            }
                        }
                    });
                }
            });

            if (isExcludedVariantsChecked && excludedVariantIds.length === 0) {
                setProductInfoDataRows([]);
                setSelectionModel([]);
            } else {
                setProductInfoDataRows(newData);
                setSelectionModel(updatedExcludedVariantIds);
            }
        }
    }, [productData]);

    const getTreeDataPath: DataGridProProps['getTreeDataPath'] = (row) => row.hierarchy;

    const getRowClassName: DataGridProProps['getRowClassName'] = (params) => {
        const isSelected = selectionModel.includes(params.row?.variants?.[0].id);
        const isParent = !params.row.isChild;

        // eslint-disable-next-line no-nested-ternary
        return isSelected && isParent
            ? 'DataGrid-ParentRow-Selected'
            : isParent
            ? 'DataGrid-ParentRow-Unselected'
            : '';
    };

    const groupingColDef: DataGridProProps['groupingColDef'] = {
        headerName: '',
        maxWidth: 70,
        renderCell: (params) => (
            <CustomGridTreeDataGroupingCellActionMenu
                id={params.id}
                rowNode={params.rowNode}
                field={params.field}
                row={params.row}
            />
        ),
        align: 'left',
    };

    const onCheckboxChecked = async (selectedRowId: GridRowSelectionModel) => {
        if (excludeSelectionCheckedRef.current) {
            const currentSelectedRowIds = new Set(selectedRowId);
            const previousSelectedRowIds = new Set(selectionModel);
            const addedRows = [...currentSelectedRowIds].filter(
                (id) => !previousSelectedRowIds.has(id)
            );
            const removedRows = [...previousSelectedRowIds].filter(
                (id) => !currentSelectedRowIds.has(id)
            );
            addUniqueIds(excludedVariantIds, addedRows);
            removeMatchingIds(excludedVariantIds, removedRows);
            setSelectionModel(selectedRowId);
            excludeSelectionCheckedRef.current = false;
        }
    };

    const onCheckAllCheckbox = async () => {
        if (!apiRef.current) return;
        const allRowIds = apiRef.current.getAllRowIds() as GridRowSelectionModel;
        const newSelectionModel = headerChecked ? allRowIds : [];
        const currentVariantIds = new Set(productInfoDataRows.map((row: any) => row.id));
        const currentSelectedRowIds = new Set(newSelectionModel);
        const previousSelectedRowIds = new Set(selectionModel);
        const addedRows = [...currentSelectedRowIds].filter(
            (id) => !previousSelectedRowIds.has(id) && currentVariantIds.has(id)
        );
        const removedRows = [...previousSelectedRowIds].filter(
            (id) => !currentSelectedRowIds.has(id) && currentVariantIds.has(id)
        );
        addUniqueIds(excludedVariantIds, addedRows);
        removeMatchingIds(excludedVariantIds, removedRows);
        apiRef.current.selectRows(newSelectionModel, true);
        setSelectionModel(newSelectionModel);
        excludeSelectionCheckedRef.current = false;
    };

    const showExcludedVariantsOnly = (checkedState: boolean | undefined) => {
        // eslint-disable-next-line no-param-reassign
        excludeSelectionCheckedRef.current = false;
        setExcludedVariantsChecked(checkedState);
        if (excludedVariantIds.length === 0) {
            setRemoveSearchFilterAndPagination(true);
        } else {
            setRemoveSearchFilterAndPagination(false);
        }
        if (isExcludedVariantsChecked) {
            setRemoveSearchFilterAndPagination(false);
        }
    };

    useEffect(() => {
        if (productInfoDataRows.length === 0) {
            setRemoveSearchFilterAndPagination(true);
        } else {
            setRemoveSearchFilterAndPagination(false);
        }
    }, [productInfoDataRows]);

    useEffect(() => {
        // onCheckAllCheckbox() is triggered when the user manually checks the header checkbox
        if (hasMounted.current && manualChange) {
            onCheckAllCheckbox();
            setManualChange(false);
        } else {
            hasMounted.current = true;
        }
    }, [headerChecked]);

    // Checks if all rows are selected to update the header checkbox
    useEffect(() => {
        const allRowIds = apiRef.current?.getAllRowIds() || [];
        const allRowsSelected =
            allRowIds.length > 0 && allRowIds.every((id) => selectionModel.includes(id));
        setHeaderChecked(allRowsSelected);
    }, [selectionModel]);

    return (
        <Container data-testid="product-list-container">
            {responseStatus !== 401 && responseStatus !== 403 && (
                <Box>
                    <Switch
                        checked={isExcludedVariantsChecked}
                        label="Show only excluded variants"
                        value="excluded"
                        onChange={showExcludedVariantsOnly}
                    />

                    <DataGridPro
                        apiRef={apiRef}
                        sx={{
                            height: productInfoDataRows.length === 0 ? '30em' : 'auto',
                            zIndex: 0,
                        }}
                        hideFooterSelectedRowCount
                        hideFooterRowCount
                        disableVirtualization
                        loading={isLoading || isFetching}
                        rowCount={(productData as any).totalRecords || 0}
                        rows={productInfoDataRows}
                        rowsLoadingMode="server"
                        columns={columns}
                        filterModel={filterModel}
                        onFilterModelChange={(model: any) => {
                            // eslint-disable-next-line no-param-reassign
                            excludeSelectionCheckedRef.current = false;
                            setFilterModel(model);
                        }}
                        sortModel={sortModel}
                        sortingMode="server"
                        onSortModelChange={(model: any) => {
                            // eslint-disable-next-line no-param-reassign
                            excludeSelectionCheckedRef.current = false;
                            setSortModel(model);
                        }}
                        pageSizeOptions={[10, 20, 50, 100]}
                        onPaginationModelChange={(model: any) => {
                            // eslint-disable-next-line no-param-reassign
                            excludeSelectionCheckedRef.current = false;
                            setPaginationModel(model);
                        }}
                        pagination={
                            productInfoDataRows.length > 0 || !removeSearchFilterAndPagination
                        }
                        paginationMode={
                            productInfoDataRows.length > 0 || !removeSearchFilterAndPagination
                                ? 'server'
                                : undefined
                        }
                        treeData
                        getTreeDataPath={getTreeDataPath}
                        getRowClassName={getRowClassName}
                        groupingColDef={groupingColDef}
                        slots={{
                            noRowsOverlay: NoProductsOverlay,
                            noResultsOverlay: NoProductsOverlay,
                        }}
                        slotProps={{
                            toolbar: {
                                quickFilterValue: filterModel?.quickFilterValues?.[0] || '',
                                numberOfExcluded: removeSearchFilterAndPagination
                                    ? '0'
                                    : excludedVariantIds.length,
                                removeSearchFilter: removeSearchFilterAndPagination,
                            },
                        }}
                        disableColumnMenu
                        checkboxSelection
                        rowSelectionModel={selectionModel}
                        onRowSelectionModelChange={onCheckboxChecked}
                    />
                </Box>
            )}
        </Container>
    );
}
