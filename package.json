{"name": "product-collection-mfe", "description": "Treez Product Collection Management MFE", "scripts": {"start": "webpack serve --port 3001 --env stage=local", "start:local:https": "webpack serve --port 3001 --env stage=local https=true", "start:standalone": "webpack serve --port 3001 --env standalone --env stage=local", "start:dev": "webpack serve --port 3001 --env stage=dev --env https=true", "start:build": "webpack serve --port 3001 --env stage=build --env https=true", "mkcert": "mkdir -p .cert && mkcert -key-file .cert/key.pem -cert-file .cert/cert.pem \"localhost\"", "build": "webpack --mode=production --env stage=prod", "build:dev": "webpack --mode=production --env stage=dev", "build:build": "webpack --mode=production --env stage=build", "build:prod": "webpack --mode=production --env stage=prod", "build:types": "tsc", "analyze": "webpack --mode=production --env analyze", "lint": "eslint .", "lint:fix": "eslint --ext .ts src/ --fix", "format": "prettier --check .", "format:fix": "prettier --write .", "test": "jest --runInBand --config ./jest.config.ts", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "cypress:open:dev": "CYPRESS_BASE_URL=https://app.sandbox.treez.io cypress open --env stage=sandbox", "cypress:run:dev": "CYPRESS_BASE_URL=https://app.sandbox.treez.io cypress run --browser=chrome --env stage=sandbox", "cypress:open:build": "CYPRESS_BASE_URL=https://app.dev.treez.io cypress open --env stage=build", "cypress:run:build": "CYPRESS_BASE_URL=https://app.dev.treez.io cypress run --browser=chrome --env stage=build"}, "version": "1.9.3", "private": true, "repository": {"type": "git", "url": "https://gitlab.com/treez-inc/engineering/front-of-house/product-collections-micro-front-end"}, "license": "UNLICENSED", "devDependencies": {"@babel/core": "^7.15.0", "@babel/eslint-parser": "^7.15.0", "@babel/plugin-transform-runtime": "^7.15.0", "@babel/preset-env": "^7.15.0", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "^7.15.0", "@babel/runtime": "^7.15.3", "@testing-library/dom": "^8.19.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^28.1.7", "@types/node": "^18.11.7", "@types/react": "^18.0.21", "@types/react-dom": "^18.0.6", "@types/systemjs": "^6.1.1", "@types/uuid": "^9.0.5", "@types/webpack-env": "^1.16.2", "@typescript-eslint/eslint-plugin": "^5.33.0", "@typescript-eslint/parser": "^5.33.0", "babel-jest": "^27.0.6", "concurrently": "^6.2.1", "cypress": "^10.6.0", "dotenv-webpack": "^8.0.1", "eslint": "^7.32.0", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.3.0", "eslint-config-ts-react-important-stuff": "^3.0.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-react": "^7.28.0", "file-loader": "^6.2.0", "history": "^5.3.0", "identity-obj-proxy": "^3.0.0", "jest": "^27.0.6", "jest-cli": "^27.0.6", "prettier": "^2.3.2", "ts-config-single-spa": "^3.0.0", "ts-node": "^10.9.1", "typescript": "^4.3.5", "webpack": "^5.51.1", "webpack-cli": "^4.8.0", "webpack-config-single-spa-react": "^4.0.0", "webpack-config-single-spa-react-ts": "^4.0.0", "webpack-config-single-spa-ts": "^4.0.0", "webpack-dev-server": "^4.0.0", "webpack-merge": "^5.8.0"}, "dependencies": {"@aws-sdk/client-ssm": "^3.496.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fontsource/roboto": "^5.0.8", "@hookform/resolvers": "^3.3.4", "@lukemorales/query-key-factory": "^1.3.4", "@mui/icons-material": "^5.15.12", "@mui/material": "^5.10.2", "@treez-inc/component-library": "^6.4.2", "@treez-inc/file-management": "^1.16.1", "axios": "^1.5.0", "jest-css-modules-transform": "^4.4.2", "joi": "^17.12.2", "moment": "^2.29.4", "react": "18.2.0", "react-dom": "18.2.0", "react-query": "^3.39.3", "react-router-dom": "^6.4.1", "single-spa": "5.9.3", "single-spa-react": "^5.0.0", "uuid": "^9.0.1"}, "types": "dist/treez-test.d.ts"}