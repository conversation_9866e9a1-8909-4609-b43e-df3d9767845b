import React from 'react';
import {render} from '@testing-library/react';
import '@testing-library/jest-dom';
import {TreezThemeProvider} from '@treez-inc/component-library';
import DeleteCollection from './index';

describe('<DeleteCollection />', () => {
    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    const renderDeleteCollectionComponent = (collectionName: string = 'test') => {
        const {getByTestId} = render(
            <TreezThemeProvider>
                <DeleteCollection name={collectionName} testid="delete-message-container" />
            </TreezThemeProvider>
        );

        const deleteMessageContainer = getByTestId('delete-message-container');
        return {
            deleteMessageContainer,
        };
    };
    it('should render the component', () => {
        const {deleteMessageContainer} = renderDeleteCollectionComponent();

        expect(deleteMessageContainer).toBeDefined();
    });

    it('should render collection name that need to be deleted', () => {
        const collectionToDelete = 'test delete collection';
        const {deleteMessageContainer} = renderDeleteCollectionComponent(collectionToDelete);

        expect(deleteMessageContainer).toBeDefined();
        expect(deleteMessageContainer).toHaveTextContent(
            'Are you sure you want to delete the collection test delete collection?'
        );
    });
});
