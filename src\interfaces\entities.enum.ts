enum Entities {
    BRAND = 'brand',
    ENTITY_PRICE = 'entity-price',
    IMAGE = 'image',
    ORGANIZATION = 'organization',
    PRODUCT = 'product',
    PRODUCT_CATEGORY = 'product-category',
    PRODUCT_SUB_CATEGORY = 'product-subcategory',
    VARIANT = 'variant',
    PRICE = 'entity-price',
    ORGANIZATION_ENTITY = 'organization-entity',
    ATTRIBUTE_CATEGORY = 'attributecategory',
    ATTRIBUTE = 'attribute',
    PRODUCT_ATTRIBUTE = 'productattribute',
    IMAGE_DETAILS = 'imageDetail',
    SEARCH_PRODUCT = 'search',
    MERGE_PRODUCT = 'merge',
}

export default Entities;
