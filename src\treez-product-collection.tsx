import React from 'react';
import * as ReactDOMClient from 'react-dom/client';
import singleSpaReact from 'single-spa-react';
import Root from './root.component';

const lifecycles = singleSpaReact({
    React,
    ReactDOMClient,
    rootComponent: Root,
    errorBoundary: (err, info, props) => (
        <>
            <div>Error: {err.toString()}</div>
            <div>Info: {info.toString()}</div>
            <div>Props: {props.toString()}</div>
        </>
    ),
});

export const {bootstrap} = lifecycles;
export const {mount} = lifecycles;
export const {unmount} = lifecycles;
