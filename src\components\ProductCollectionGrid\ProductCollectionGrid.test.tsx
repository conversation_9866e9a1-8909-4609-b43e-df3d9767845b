import React from 'react';
import {fireEvent, render, screen} from '@testing-library/react';
import '@testing-library/jest-dom';
import {TreezThemeProvider} from '@treez-inc/component-library';
import {MemoryRouter} from 'react-router-dom';
import ProductCollectionGrid from '.';
import {productCollectionListData} from '../../test/testData';
import {MFEPermissions} from '../../interfaces/permissions';
import {IProductCollectionData} from '../../interfaces/productCollectionResponse';

jest.mock('../../hooks/usePermissionsContext');
const mockUserPermissionsContext = require('../../hooks/usePermissionsContext').default;

const mockData = productCollectionListData.data;
const orgId = '4a873b6d-e350-4955-a254-bb37b8bc81f6';
const permission = {
    permissions: {[MFEPermissions.MANAGE_PRODUCT_COLLECTIONS]: true},
    orgId,
};
const setIsLoading = jest.fn();

describe('<ProductCollectionGrid />', () => {
    beforeEach(() => {
        mockUserPermissionsContext.mockImplementation(() => permission);
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    const renderCollectionsComponent = (
        collectionMockData: IProductCollectionData[],
        loading: boolean
    ) => {
        const {queryByText, queryAllByTestId, getByTestId, getByRole} = render(
            <MemoryRouter>
                <TreezThemeProvider>
                    <ProductCollectionGrid data={collectionMockData} loading={loading} setIsLoading={setIsLoading}/>
                </TreezThemeProvider>
            </MemoryRouter>
        );

        const flyOutMenuIcon = queryAllByTestId('collections-flyout-menu-more');
        const firstFlyoutMenuIcon = flyOutMenuIcon[0] ?? null;

        return {
            queryByText,
            getByTestId,
            getByRole,
            firstFlyoutMenuIcon,
        };
    };

    it('renders ProductCollectionGrid with data', async () => {
        mockUserPermissionsContext.mockImplementation(() => ({
            permissions: {[MFEPermissions.MANAGE_PRODUCT_COLLECTIONS]: true},
            orgId,
        }));

        renderCollectionsComponent(mockData, false);

        expect(screen.getByTestId('collections-count')).toHaveTextContent('3 Collections');
        expect(screen.getByRole('grid')).toBeInTheDocument();
        expect(screen.getByText('Collection')).toBeInTheDocument();
        expect(screen.getByText('Last update')).toBeInTheDocument();
        mockData.forEach((collection) => {
            if (collection.deletedAt === null) {
                expect(screen.getByText(collection.name)).toBeInTheDocument();
            }
        });
    });

    it('renders ProductCollectionGrid with loading state', () => {
        renderCollectionsComponent(mockData, true);
        expect(screen.getByTestId('collections-count')).toHaveTextContent(`3 Collections`);
        expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('renders collectionMenu', async () => {
        mockUserPermissionsContext.mockImplementation(() => ({
            permissions: {[MFEPermissions.MANAGE_PRODUCT_COLLECTIONS]: true},
            orgId,
        }));
        const {firstFlyoutMenuIcon} = renderCollectionsComponent(mockData, false);
        expect(firstFlyoutMenuIcon).toBeVisible();
        fireEvent.click(firstFlyoutMenuIcon);
        expect(screen.getByTestId('collection-action-delete')).toHaveTextContent('Delete');
    });

    it('should not render collectionMenu for invalid permission', async () => {
        mockUserPermissionsContext.mockImplementation(() => ({
            permissions: {[MFEPermissions.VIEW_PRODUCT_COLLECTIONS]: true},
            orgId,
        }));
        const {firstFlyoutMenuIcon} = renderCollectionsComponent(mockData, false);
        expect(firstFlyoutMenuIcon).toBeNull();
    });
});
