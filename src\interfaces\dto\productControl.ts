export interface VariantPropertiesProps {
    size: string;
    sizeLabel: string;
    merchandiseSize: string;
}

export interface MenuItemsProps {
    displayValue: string | number;
    displayName: string;
}
export interface IFilterState {
    subCategory?: string[];
    status?: string[];
}

export interface IFilterCheckbox {
    key: string;
    label: string;
    checked: boolean;
    onChange: (checkboxKey: string | number, isChecked: boolean) => void;
    value: string;
}

export interface IFilterCheckboxItem {
    key: string;
    value: string;
}

export interface IFilterDropdown {
    filterItem: string;
    label: string;
    chipId: string;
    menuId: string;
    values: any;
    onChange: (a: string, b: IFilterCheckbox[]) => void;
    badgeContent?: number | string;
    subCategoryList?: any;
    status?: string[];
}

export interface IFilterComponent {
    categoryData: any;
    onChange: any;
    subCategoryData: {
        displayName: string;
        displayValue: string;
        categoryId: string;
    };
}

export interface ICategoryList {
    displayName: string;
    displayValue: string;
    categoryId: string;
}

export enum Status {
    ACTIVE = 'active',
    INACTIVE = 'inactive',
    DRAFT = 'draft',
    MERGE = 'merge',
}

export interface ProductStatusCheckbox {
    key: Status;
    value: string;
    label: string;
    checked?: boolean;
}

export interface IHeaders {
    key: string | number;
    label: string;
    onChange?: (headerKey: string | number) => unknown;
    testId?: string;
    value?: any;
}

export const productStatusMenuItems: MenuItemsProps[] = [
    {
        displayName: Status.ACTIVE,
        displayValue: Status.ACTIVE,
    },
    {
        displayName: Status.INACTIVE,
        displayValue: Status.INACTIVE,
    },
];
