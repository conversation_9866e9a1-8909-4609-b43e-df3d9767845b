import React from 'react';
import {render} from '@testing-library/react';
import {TreezThemeProvider} from '@treez-inc/component-library';
import '@testing-library/jest-dom';
import PermissionsError from '.';

describe('<ErrorPermissions />', () => {
    const errorMessage = {
        title: "You don't have access to this page.",
        message: 'Contact your Treez Administrator to request permission.',
    };
    const renderErrorPermissionsComponent = () => {
        const {getByText, getByTestId} = render(
            <TreezThemeProvider>
                <PermissionsError />
            </TreezThemeProvider>
        );

        return {getByTestId, getByText};
    };

    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    it('should render', async () => {
        const {getByTestId, getByText} = renderErrorPermissionsComponent();
        expect(getByTestId('no-permission-error-template')).toBeInTheDocument();
        expect(getByText(errorMessage.title)).toBeInTheDocument();
        expect(getByText(errorMessage.message)).toBeInTheDocument();
    });
});
