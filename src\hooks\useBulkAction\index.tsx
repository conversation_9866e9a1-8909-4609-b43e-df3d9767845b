
import {useState} from 'react';
import {GridRowId, GridRowSelectionModel} from '@mui/x-data-grid-pro';
import {IProduct} from '../../interfaces/product';
import {
    calculateSelectedVariantsLength,
    getProductByVariantIds,
    getSelectedProductsData,
} from '../../utils/filterUtil';

interface GetBulkActionBarProps {
    showConfirmation: () => void;
}

const useBulkAction = (modifiedData: IProduct[]) => {
    const [selectionModel, setSelectionModel] = useState<GridRowSelectionModel>([]);
    const [totalSelectedVariants, setTotalSelectedVariants] = useState(0);

    const selectedProductsCount = getProductByVariantIds(modifiedData, selectionModel)?.length;

    const getBulkActionBarProps = ({showConfirmation}: GetBulkActionBarProps) => {
        const activateButtonProps = selectedProductsCount >= 1 && {
            label: 'Remove from collection',
            onClick: () => {
                showConfirmation();
            },
            iconName: 'Delete',
        };

        const buttonProps = [activateButtonProps].filter(Boolean);

        return {
            buttonProps,
            variant: 'primary',
        };
    };

    const handleSelectionModelChange = (selectedRowId: GridRowSelectionModel) => {
        const selectedVariantsLength = calculateSelectedVariantsLength(selectedRowId, modifiedData);
        setTotalSelectedVariants(selectedVariantsLength);

        const allSelectedProduct = modifiedData.filter(
            (productInfo: IProduct) => selectedRowId.indexOf(productInfo.productId) >= 0
        );
        if (allSelectedProduct && allSelectedProduct.length > 0) {
            const selectedProductId = allSelectedProduct.flatMap((p) => p.id);
            const existingRowIds = selectedRowId.map((id) => id);
            selectedProductId.forEach((id) => {
                if (existingRowIds.indexOf(id) < 0) {
                    existingRowIds.push(id);
                }
            });
            setSelectionModel(existingRowIds);
        } else {
            setSelectionModel(selectedRowId);
        }

        const checkedItem = selectionModel;
        checkedItem.forEach((rowId: GridRowId) => {
            const itemNotFound = selectedRowId.indexOf(rowId) < 0;
            if (itemNotFound) {
                const allProductInfoRows = modifiedData.filter(
                    (productInfo: IProduct) =>
                        productInfo.productId === rowId.toString() && productInfo.isChild
                );
                const allSelectedProductIds: string[] = allProductInfoRows.flatMap(
                    (product) => product.id
                );
                allSelectedProductIds.forEach((id: string) => {
                    if (selectedRowId.indexOf(id) >= 0) {
                        selectedRowId.splice(selectedRowId.indexOf(id), 1);
                    }
                });
                setSelectionModel(selectedRowId);
            }
        });
    };

    return {
        totalSelectedVariants,
        selectionModel,
        getSelectedProductsData,
        calculateSelectedVariantsLength,
        handleSelectionModelChange,
        getBulkActionBarProps,
        selectedProductsCount,
    };
};

export default useBulkAction;
