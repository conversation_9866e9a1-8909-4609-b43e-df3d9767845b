import {useEffect, useState} from 'react';
import useGetRequest from '../useGetRequest';
import {GetAllCollectionsResponseBody} from '../../interfaces/apiResponses';
import {IProductCollectionData} from '../../interfaces/productCollectionResponse';
import {COLLECTIONS_URL} from '../../constants/apiEndPoints';

const useProductCollections = () => {
    const [currentProductCollectionState, setCurrentProductCollectionState] =
        useState<GetAllCollectionsResponseBody | null>({
            totalCount: 0,
            data: [],
        });
    const state = useGetRequest<GetAllCollectionsResponseBody>(COLLECTIONS_URL);

    const {data: response} = state;

    useEffect(() => {
        if (response?.totalCount !== undefined) {
            setCurrentProductCollectionState(response);
        }
    }, [response?.totalCount]);

    const appendCollection = (collection: IProductCollectionData) => {
        const existingCollection = currentProductCollectionState?.data.find((item) => item.id === collection.id);

        if (existingCollection && existingCollection.name === collection.name) {
            return;
        }

        if (currentProductCollectionState) {
            const updatedCollectionState: GetAllCollectionsResponseBody = {
                ...currentProductCollectionState,
                totalCount: currentProductCollectionState.totalCount + 1,
                data: [collection, ...currentProductCollectionState.data.filter(item => item.id !== collection.id)],
            };
            setCurrentProductCollectionState(updatedCollectionState);
        }
    };

    const deleteCollection = (collection: IProductCollectionData) => {
        setCurrentProductCollectionState((prev) => {
            const curr = {...prev};
            const deleteCollectionIndex = curr?.data?.findIndex((e) => e.id === collection.id);

            if (deleteCollectionIndex === -1 || deleteCollectionIndex === undefined) {
                return curr as GetAllCollectionsResponseBody | null;
            }

            if (curr.data && curr.data[deleteCollectionIndex]) {
                curr.data[deleteCollectionIndex].deletedAt = collection.deletedAt;
                curr.data[deleteCollectionIndex].updatedAt = collection.updatedAt;
            }

            return curr as GetAllCollectionsResponseBody;
        });
    };

    return {...state, data: currentProductCollectionState, appendCollection, deleteCollection};
};

export default useProductCollections;
