export const COLLECTION_TYPE = {
    Manual: 'Manual',
    Automated: 'Automated',
};

export const COLLECTION_RULES = [
    {key: 'brand', label: 'Brand'},
    {key: 'amount', label: 'Amount'},
    {key: 'classification', label: 'Classification'},
    {key: 'effect-attributes', label: 'Effect Attributes'},
    {key: 'flavor-attributes', label: 'Flavor Attributes'},
    {key: 'general-attributes', label: 'General Attributes'},
    {key: 'ingredient', label: 'Ingredient'},
    {key: 'name', label: 'Name'},
    {key: 'size', label: 'Size'},
    {key: 'subtype', label: 'Subtype'},
    {key: 'tag', label: 'Tag'},
    {key: 'uom', label: 'UOM'},
];

export const PRODUCT_CLASSIFICATION_OPTIONS = [
    {displayName: 'Sativa', displayValue: 'Sativa'},
    {displayName: 'Indica', displayValue: 'Indica'},
    {displayName: 'Hybrid', displayValue: 'Hybrid'},
    {displayName: 'I/S', displayValue: 'I/S'},
    {displayName: 'S/I', displayValue: 'S/I'},
    {displayName: 'CBD', displayValue: 'CBD'},
    {displayName: 'Kush', displayValue: 'Kush'},
    {displayName: 'None', displayValue: 'None'},
];

export const PRODUCT_TYPE_OPTIONS = [
    {displayName: 'SAMPLE', displayValue: 'sample'},
    {displayName: 'PROMO', displayValue: 'promo'},
    {displayName: 'BASE SKU', displayValue: 'baseSKU'},
];
export const AUTOMATED_COLLECTION_RULES: any[] = [
    {
        name: 'SKU Type',
        ruleType: 'skuType',
        inputType: 'multiselect',
        options: [],
        values: [],
        custom: false,
        active: true,
    },
    {
        name: 'Brand',
        ruleType: 'brand',
        inputType: 'multiselect',
        options: [],
        values: [],
        custom: false,
        active: true,
    },
    {
        name: 'Price',
        ruleType: 'basePrice',
        inputType: 'multiselect',
        options: [],
        values: [],
        custom: true,
        active: false,
    },
    {
        name: 'Classification',
        ruleType: 'classification',
        inputType: 'multiselect',
        options: [],
        values: [],
        custom: false,
        active: true,
    },
    {
        name: 'Effects',
        ruleType: 'attr_effects',
        inputType: 'multiselect',
        options: [],
        values: [],
        custom: false,
        active: true,
    },
    {
        name: 'Flavor',
        ruleType: 'attr_flavor',
        inputType: 'multiselect',
        options: [],
        values: [],
        custom: false,
        active: true,
    },
    {
        name: 'General',
        ruleType: 'attr_general',
        inputType: 'multiselect',
        options: [],
        values: [],
        custom: false,
        active: true,
    },
    {
        name: 'Ingredients',
        ruleType: 'attr_ingredients',
        inputType: 'multiselect',
        options: [],
        values: [],
        custom: false,
        active: true,
    },
    {
        name: 'Name',
        ruleType: 'productName',
        inputType: 'multiselect',
        options: [],
        values: [],
        custom: true,
        active: false,
    },
    {
        name: 'Amount',
        ruleType: 'amount',
        inputType: 'multiselect',
        options: [],
        values: [],
        custom: false,
        active: true,
    },
    {
        name: 'Subcategory',
        ruleType: 'subCategory',
        inputType: 'multiselect',
        options: [],
        values: [],
        custom: false,
        active: true,
    },
    {
        name: 'Internal Tags',
        ruleType: 'attr_internaltags',
        inputType: 'multiselect',
        options: [],
        values: [],
        custom: false,
        active: true,
    },
    {
        name: 'UOM',
        ruleType: 'uom',
        inputType: 'multiselect',
        options: [],
        values: [],
        custom: false,
        active: false,
    },
];
