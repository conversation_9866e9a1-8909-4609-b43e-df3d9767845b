import React, { useEffect, useState } from "react";
import {debounce} from '@mui/material/utils';
import { convertPxToRem, RadioButton } from "@treez-inc/component-library";
import styled from "@emotion/styled";
import { useNavigate } from "react-router-dom";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import CollectionModal from "../../components/CollectionModal";
import { COLLECTION_TYPE } from "../../constants/productCollection";
import AddCollection from "../../components/AddCollection";
import { CollectionFormData, CollectionFormError } from "../../interfaces/collectionForm";
import collectionUtil from '../../utils/collectionForm';
import { IProductCollectionData } from "../../interfaces/productCollectionResponse";
import { CollectionCreateDTO } from "../../interfaces/productCollection";
import { COLLECTIONS_URL } from "../../constants/apiEndPoints";
import useApiContext from "../../hooks/useApiContext";
import useSnackbarContext from "../../hooks/useSnackbarContext";

const StyledCollectionDetailsDiv = styled("div")(() => ({
    width: '100%',
    fontWeight: 'normal',
    display: 'flex',
    flexDirection: 'column',
    gap: convertPxToRem(10)
}));

interface IAddProductcollection {
    closeModal: () => void;
    productCollectionData: IProductCollectionData[];
    setIsLoading: (loading: boolean) => void;
}

const AddProductCollection: React.FC<IAddProductcollection> = ({
    closeModal,
    productCollectionData,
    setIsLoading
}) => {
    const {api} = useApiContext();
    const {setSnackbar} = useSnackbarContext();
    const navigate = useNavigate();
    const [isAddDisabled, setAddDisabled] = useState(false);
    const [collectionType, setCollectionType] = useState(COLLECTION_TYPE.Manual);
    const [formData, setFormData] = useState<CollectionFormData>({collectionName: ''});
    const [formError, setFormError] = useState<{
        [Key in keyof CollectionFormData]: CollectionFormError;
    }>({
        collectionName: {isError: false, message: ''},
    });
    const resetForm = () => setFormData({collectionName: ''});

    const collectionTypeChange = () => {
        setCollectionType(collectionType === COLLECTION_TYPE.Manual ? COLLECTION_TYPE.Automated : COLLECTION_TYPE.Manual);
        setAddDisabled(false);
    }

    const handleCollectionCreate = async (e: React.FormEvent) => {
        e.preventDefault();
        closeModal();
        setAddDisabled(true);
        setIsLoading(true);

        const body: CollectionCreateDTO = {
            name: formData.collectionName,
            collectionItems: [],
            sync: collectionType === COLLECTION_TYPE.Automated,
            ...(collectionType === COLLECTION_TYPE.Automated && { rule: {} }),
        };

        try {
            const { data } = await api.post(COLLECTIONS_URL, body);
            setSnackbar({
                message: `Collection ${data.name} is created.`,
                severity: 'info',
                iconName: 'Success',
            });
            resetForm();
            const collectionId = data?.id;
            const collectionName = data?.name;
            const encodedCollectionName = btoa(collectionName);

            if (collectionType === COLLECTION_TYPE.Manual) {
                navigate(`/product-collection/${collectionId}?n=${encodedCollectionName}`);
            } else {
                navigate(`/product-collection/automated/${collectionId}`);
            }
        } catch (error) {
            setSnackbar({
                message: `We cannot create ${formData.collectionName} collection. Please try again later.`,
                severity: 'error',
                iconName: 'Error',
            });
        }

        setAddDisabled(false);
        setIsLoading(false);
    };

    const debounceFunction = React.useCallback(
        debounce(async (value: string, key: string) => {
            const isCollectionNameExists = collectionUtil.checkActiveCollectionNameExist(
                productCollectionData,
                value,
                undefined,
            );
            const isMaxLimitReached = collectionUtil.checkCollectionNameLength(value);
            const isNameEmpty = collectionUtil.collectionNameLengthIsEmpty(value);
            setFormError((err) => ({
                ...err,
                [key]: {
                    isError: isCollectionNameExists || isMaxLimitReached || isNameEmpty,
                    message: collectionUtil.collectionFormErrorMessage(
                        isNameEmpty,
                        isMaxLimitReached,
                        isCollectionNameExists
                    ),

                },
            }));
        }, 500),
        []
    );

    useEffect(() => {
        const isError = Object.values(formError).some((error) => error.isError);
        setAddDisabled(formData.collectionName === '' || isError);
    }, [formData, formError]);

    return (
        <CollectionModal
            isOpen
            primaryButton={{
                onClick: handleCollectionCreate,
                label: "Create",
                isDisabled: isAddDisabled,
            }}
            secondaryButton={{
                onClose: closeModal,
                label: "Cancel",
                isDisabled: isAddDisabled,
            }}
            title="Collections"
            testId="add-collection-modal"
        >
            <Box display="grid" gap={1}>
                <Box>
                    <StyledCollectionDetailsDiv>
                        <Typography variant="largeText">Users can create two types of collections -- Manual & Automated</Typography>
                        <Typography variant="largeText">Manual collections are built buy finding a specific product in product control and selecting an action to add it to a manual collection.</Typography>
                        <Typography variant="largeText">Automated collections are built by creating a set of rules that products must meet to be in the collection. Any new products that meet those rules are added to the collection by default, but can be explicit excluded in the UI.</Typography>
                    </StyledCollectionDetailsDiv>
                </Box>
                <Box>
                    <RadioButton
                        defaultValue={collectionType}
                        label=""
                        onChange={collectionTypeChange}
                        radioButtons={[
                            {
                                label: 'Manual',
                                value: COLLECTION_TYPE.Manual
                            },
                            {
                                label: 'Automated',
                                value: COLLECTION_TYPE.Automated
                            }
                        ]}
                        row
                    /> 
                </Box>
                <Box>
                    <AddCollection
                        formData={formData}
                        setFormData={setFormData}
                        formError={formError}
                        debounceFunction={debounceFunction}
                    />
                </Box>
            </Box>
        </CollectionModal>
    );
}

export default AddProductCollection;