import React from 'react';
import '@testing-library/jest-dom';
import {TreezThemeProvider} from '@treez-inc/component-library';
import {render} from '@testing-library/react';
import {AxiosError} from 'axios';
import RouteError from '.';

const testIdErrorNotFound = 'error-not-found';
const testIdErrorDefault = 'error-default';
const testIdErrorNoPermission = 'error-no-permission';

jest.mock('../ErrorNotFound', () => () => <div data-testid={testIdErrorNotFound} />);
jest.mock('../ErrorDefault', () => () => <div data-testid={testIdErrorDefault} />);
jest.mock('../ErrorPermissions', () => () => <div data-testid={testIdErrorNoPermission} />);

const createAxiosError = (status: number) =>
    // @ts-expect-error
    new AxiosError('error', 'code', {}, 'request', {
        data: 'data',
        status,
        statusText: 'statusText',
        headers: {},
        config: {},
    });

describe('<RouteError />', () => {
    const renderRouteErrorComponent = (error: unknown) => {
        const {queryByTestId} = render(
            <TreezThemeProvider>
                <RouteError error={error} />
            </TreezThemeProvider>
        );

        const errorNotFound = queryByTestId(testIdErrorNotFound);
        const errorDefault = queryByTestId(testIdErrorDefault);
        const errorNoPermission = queryByTestId(testIdErrorNoPermission);

        return {
            errorNotFound,
            errorDefault,
            errorNoPermission,
        };
    };

    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    it('returns ErrorNotFound when response status is 404', async () => {
        const error = createAxiosError(404);

        const {errorNotFound} = renderRouteErrorComponent(error);

        expect(errorNotFound).toBeInTheDocument();
    });

    it('returns ErrorDefault when response status is 500', async () => {
        const error = createAxiosError(500);

        const {errorDefault} = renderRouteErrorComponent(error);

        expect(errorDefault).toBeInTheDocument();
    });

    it('returns ErrorDefault when error is not axios error', async () => {
        const error = new Error();

        const {errorDefault} = renderRouteErrorComponent(error);

        expect(errorDefault).toBeInTheDocument();
    });

    it('returns ErrorNoPermission when response status is 403', async () => {
        const error = createAxiosError(403);

        const {errorNoPermission} = renderRouteErrorComponent(error);

        expect(errorNoPermission).toBeInTheDocument();
    });
});
