import React, {useEffect, useRef, useState} from 'react';
import {convertPxToRem, Tooltip, Icon} from '@treez-inc/component-library';
import {Box, styled, Typography} from '@mui/material';
import {useNavigate, useParams} from 'react-router-dom';
import {FormProvider, useForm} from 'react-hook-form';
import {GridRowSelectionModel} from '@mui/x-data-grid-pro';
import PageLayout from '../../components/PageLayout';
import {
    AutomatedCollectionFormData,
    CollectionFormError,
    CollectionItems,
} from '../../interfaces/collectionForm';
import collectionUtil from '../../utils/collectionForm';
import AddAutomatedCollectionName from './AddAutomatedCollectionName';
import CollectionFormButtons from './CollectionFormButtons';
import {
    AUTOMATED_COLLECTION_FORM_TITLE_CREATE,
    AUTOMATED_COLLECTION_FORM_TITLE_EDIT,
    AUTOMATED_COLLECTION_FORM_TITLE_TOOLTIP,
    PRODUCT_CATEGORY_TITLE,
    PRODUCT_COLLECTION_RULES_TITLE,
} from '../../constants/strings';
import IProductCollection, {CollectionCreateDTO, IRule} from '../../interfaces/productCollection';
import useSnackbarContext from '../../hooks/useSnackbarContext';
import RouteError from '../../components/RouteError';
import useProductCollections from '../../hooks/useProductCollections';
import useGetAllCategoryOptions from './ProductCategory/useGetAllCategoryOptions';
import {ProductCategoryDto} from '../../interfaces/dto/productCategory';
import ProductCategories from './ProductCategory';
import ProductCollectionRules from './ProductCollectionRules';
import ProductList from './ProductListView';
import useGetAutomatedCollectionDetails from '../../hooks/useGetProductCollectionDetails';
import AutomatedCollectionConfirmView from './AutomatedCollectionConfirmView';
import useGetAttributeCategoriesOptions from '../../hooks/useGetAttributeCategoriesOptions';
import useGetProductSubCategoriesOptions from '../../hooks/useGetProductSubCategoriesOptions';
import useGetProductBrandsOptions from '../../hooks/useGetProductBrandsOptions';
import useSaveData from '../../hooks/useSaveData';
import collectionApiKeyStore from '../../api/collectionApiKeyStore';
import {delay, isEmpty} from '../../utils/common';
import {globalCategory, GLOBAL_CATEGORY_NAME} from '../../utils/constants';
import {removeEmptyArrays} from '../../utils/filterUtil';
import {deepClone} from '../../utils/deepClone';

const AutomatedCollectionTitleContainer = styled(Box)({
    display: 'flex',
    textAlign: 'left',
    alignItems: 'center',
    flexDirection: 'row',
});

const StyledIconContainer = styled(Box)(() => ({
    display: 'flex',
    marginLeft: convertPxToRem(5),
}));

const AutomatedCollectionFormContainer = styled(Box)(() => ({
    padding: convertPxToRem(32),
    display: 'flex',
    flexDirection: 'column',
    gap: convertPxToRem(16),
    height: '100%',
}));

const ProductTypeLineTitleContainer = styled(Box)({
    paddingTop: convertPxToRem(20),
    display: 'flex',
    textAlign: 'left',
    alignItems: 'center',
    flexDirection: 'row',
});

const ProductCollectionRulesTitleContainer = styled(Box)({
    paddingTop: convertPxToRem(20),
    paddingBottom: convertPxToRem(10),
    display: 'flex',
    textAlign: 'left',
    alignItems: 'center',
    flexDirection: 'row',
});

const AutomatedCollectionForm: React.FC = () => {
    const {id = ''} = useParams<{id: string}>();
    const {setSnackbar} = useSnackbarContext();
    const navigate = useNavigate();
    const [isAddAutomatedCollectionDisabled, setAddAutomatedCollectionDisabled] = useState(true);
    const [selectedRules, setSelectedRules] = useState<IRule>();
    const [productCollections, setProductCollections] = useState<IProductCollection[]>([]);
    const [collectionProductCategory, setCollectionProductCategory] =
        useState<ProductCategoryDto>();
    const [submitType, setSubmitType] = useState<string>(id !== '' ? 'Save' : 'Create');
    const [excludedVariantIds, setExcludedVariantIds] = useState<GridRowSelectionModel>([]);
    const [isCreateAutomatedCollectionModal, setCreateAutomatedCollectionModal] = useState(false);
    const [isDeletedCollection, setDeletedCollection] = useState<boolean>(false);
    const excludeSelectionCheckedRef = useRef(false);
    const [isUpdatePending, setIsUpdatePending] = useState(false);
    // This change state is for checking if there are any differences from the current data and newly updated data
    const [change, setChange] = useState<boolean>(false);
    const clonedExcludedVariantIds = useRef<GridRowSelectionModel>([]);
    const clonedSelectedCategory = useRef<string>();
    const clonedFormDataName = useRef<string>('');
    const clonedFormDataRule = useRef<IRule>();

    const {data: categoryData} = useGetAllCategoryOptions();
    const allCategoryOptions: ProductCategoryDto[] = categoryData;

    const {data: collectionDetailsData, loading: isCollectionDetailsLoading} =
        useGetAutomatedCollectionDetails(id);
    const {data: attributeCategoriesData} = useGetAttributeCategoriesOptions();
    const {data: subCategoriesData} = useGetProductSubCategoriesOptions();
    const {data: brandsData} = useGetProductBrandsOptions();

    const initialFormData = {
        collectionName: '',
        sync: true,
        rule: [],
        collectionItems: [],
    };

    const [formData, setFormData] = useState<AutomatedCollectionFormData>(initialFormData);
    const [formError, setFormError] = useState<{
        [Key in keyof AutomatedCollectionFormData]: CollectionFormError;
    }>({
        collectionName: {isError: false, message: ''},
        sync: {isError: false, message: ''},
        rule: {isError: false, message: ''},
        collectionItems: {isError: false, message: ''},
    });

    const {
        data: productCollectionData,
        loading: productCollectionLoading,
        error: productCollectionError,
    } = useProductCollections();

    if (productCollectionError !== null) {
        return <RouteError error={productCollectionError} />;
    }

    const {mutateAsync} = useSaveData<CollectionCreateDTO[]>({
        mutationConfig: collectionApiKeyStore.upsertProductCollection(
            formData?.id ? formData.id : ''
        ),
    });

    useEffect(() => {
        if (!productCollectionLoading && productCollectionData) {
            setProductCollections(productCollectionData?.data);
        }
    }, [productCollectionData]);

    if (categoryData) {
        if (allCategoryOptions.length > 0) {
            allCategoryOptions.sort((a: any, b: any) => {
                if (a.name === GLOBAL_CATEGORY_NAME) {
                    return -1;
                }

                if (b.name === GLOBAL_CATEGORY_NAME) {
                    return 1;
                }

                return a.name.toUpperCase() > b.name.toUpperCase() ? 1 : -1;
            });
        }

        const isGlobalCategoryExists =
            allCategoryOptions.length > 0
                ? allCategoryOptions.some((category) => category.id === 'global')
                : undefined;

        if (allCategoryOptions.length > 0 && !isGlobalCategoryExists) {
            allCategoryOptions.unshift(globalCategory);
        }

        if (
            !isCollectionDetailsLoading &&
            !formData.id &&
            globalCategory &&
            !collectionProductCategory
        ) {
            setCollectionProductCategory(globalCategory);
        }
    }

    const resetForm = () =>
        setFormData({collectionName: '', sync: true, rule: [], collectionItems: []});

    const handleSubmit = (): boolean => {
        const value = formData.collectionName;
        const key = 'collectionName';

        const isCollectionNameExists = collectionUtil.checkActiveCollectionNameExist(
            productCollections,
            value,
            collectionDetailsData?.name
        );

        const isMaxLimitReached = collectionUtil.checkCollectionNameLength(value);
        const isNameEmpty = collectionUtil.collectionNameLengthIsEmpty(value);

        const hasError = isCollectionNameExists || isMaxLimitReached || isNameEmpty;

        if (hasError) {
            setFormError((err) => ({
                ...err,
                [key]: {
                    isError: true,
                    message: collectionUtil.collectionFormErrorMessage(
                        isNameEmpty,
                        isMaxLimitReached,
                        isCollectionNameExists
                    ),
                },
            }));
            return false;
        }

        return true;
    };

    const closeAutomatedCollectionForm = (collection: any | null) => {
        if (change) {
            setCreateAutomatedCollectionModal(true);
        } else {
            setCreateAutomatedCollectionModal(false);
            if (collection?.id) {
                navigate('/product-collection', {state: {collection}});
            } else {
                navigate('/product-collection');
            }
        }
    };

    const hasCollectionExecuted = useRef(false);
    useEffect(() => {
        if (
            !hasCollectionExecuted.current &&
            !isCollectionDetailsLoading &&
            collectionDetailsData &&
            allCategoryOptions
        ) {
            const existingFormData = {
                id: collectionDetailsData.id,
                collectionName: collectionDetailsData ? collectionDetailsData.name : '',
                sync: collectionDetailsData.sync,
                rule: collectionDetailsData.rule,
                collectionItems: collectionDetailsData.collectionItems,
            };
            setFormData(existingFormData);
            clonedFormDataName.current = deepClone(existingFormData.collectionName);
            clonedFormDataRule.current = deepClone(collectionDetailsData.rule);

            setSubmitType(id !== '' ? 'Save' : 'Create');

            if (collectionDetailsData.rule && 'category' in collectionDetailsData.rule) {
                if (collectionDetailsData.rule && collectionDetailsData.rule.category) {
                    const categoryId: any = collectionDetailsData.rule?.category[0];
                    if (allCategoryOptions && allCategoryOptions?.length > 0) {
                        const productCategory = allCategoryOptions.find(
                            (item) => item.id === categoryId
                        );
                        setCollectionProductCategory(productCategory);
                        clonedSelectedCategory.current = deepClone(productCategory?.id);
                    }
                }
            } else {
                setCollectionProductCategory(globalCategory);
            }

            if (collectionDetailsData.collectionItems) {
                const excludedVariants = collectionDetailsData.collectionItems
                    .filter((item) => item.isExclude === true)
                    .map((item) => item.variantId);
                setExcludedVariantIds(excludedVariants);
                clonedExcludedVariantIds.current = deepClone(excludedVariants);
            }

            if (collectionDetailsData?.deletedAt) {
                setDeletedCollection(true);
            } else {
                setDeletedCollection(false);
            }
            hasCollectionExecuted.current = true;
        }
    }, [
        isCollectionDetailsLoading,
        categoryData,
        collectionDetailsData,
        collectionProductCategory,
    ]);

    useEffect(() => {
        /*
            Disables the save button if:
            - Any form field has an error (error.isError === true)
            - collectionName is missing or empty
            - collectionProductCategory is not set
        */
        const isError = Object.values(formError).some((error) => error.isError);
        setAddAutomatedCollectionDisabled(
            !formData.collectionName ||
                formData.collectionName === '' ||
                !collectionProductCategory ||
                isError
        );
    }, [formData, formError]);

    useEffect(() => {
        const isDifferent = () => {
            const oldArr = clonedExcludedVariantIds.current;
            const newArr = excludedVariantIds;

            if (oldArr.length !== newArr.length) return true;

            const sortedOld = [...oldArr].sort();
            const sortedNew = [...newArr].sort();

            return !sortedOld.every((val, index) => val === sortedNew[index]);
        };

        const isCategoryDifferent = () => {
            let oldCategoryId = '';
            if (clonedSelectedCategory.current === undefined) {
                oldCategoryId = 'global';
            } else {
                oldCategoryId = clonedSelectedCategory.current;
            }
            const newCategory = collectionProductCategory;
            return oldCategoryId !== newCategory?.id;
        };

        const isNameDifferent = () => {
            const oldName = clonedFormDataName.current;
            const newName = formData.collectionName;

            return oldName !== newName;
        };

        const isDifferentRule = () => {
            const selected = selectedRules;
            const cloned = clonedFormDataRule.current;

            if (!selected || !cloned) {
                return true;
            }

            const allKeys = new Set([...Object.keys(selected), ...Object.keys(cloned)]);

            // Checks if the length matches
            const hasDifferences = Array.from(allKeys).some((key) => {
                const selectedVal = selected[key as keyof IRule] ?? [];
                const clonedVal = cloned[key as keyof IRule] ?? [];

                // returns true if lengths do not match
                if (selectedVal.length !== clonedVal.length) {
                    return true;
                }

                // If the lengths match, now check if the content of the arrays is the same.
                const arraysEqual =
                    selectedVal.every((item) => clonedVal.includes(item)) &&
                    clonedVal.every((item) => selectedVal.includes(item));

                return !arraysEqual;
            });

            return hasDifferences;
        };

        const fromChange =
            isDifferent() || isCategoryDifferent() || isNameDifferent() || isDifferentRule();
        setChange(fromChange);
        setIsUpdatePending(fromChange);
    }, [excludedVariantIds, collectionProductCategory, formData.collectionName, selectedRules]);

    const updateExcludedVariants = async () => {
        try {
            const updatedCollectionItems = collectionUtil.mergeExcludedVariants(
                collectionDetailsData?.collectionItems,
                excludedVariantIds as any
            );

            const excludedCollectionItems = updatedCollectionItems
                .filter((item: any) => item.isExclude === true)
                .map((item: any) => item.variantId);

            setExcludedVariantIds(excludedCollectionItems);
        } catch (collectionUpdateError) {
            setSnackbar({
                message: `We can not update ${formData.collectionName} collection. Please try again later.`,
                severity: 'error',
                iconName: 'Error',
            });
        } finally {
            setIsUpdatePending(false);
        }
    };

    const createUpdateAutomatedCollection = async () => {
        const isValid = handleSubmit();
        if (!isValid) return;
        const mergedVariantsMap = new Map<string, CollectionItems>();
        const existingExcludedVariants: CollectionItems[] | [] | undefined =
            collectionDetailsData?.collectionItems
                ? collectionDetailsData?.collectionItems.map((item) => ({
                      variantId: item.variantId,
                      isExclude: item.isExclude,
                  }))
                : [];
        const currentlyExcludedVariants: CollectionItems[] | [] | undefined = excludedVariantIds
            ? excludedVariantIds.map((item: any) => ({
                  variantId: item,
                  isExclude: true,
              }))
            : [];

        existingExcludedVariants?.forEach((item) => {
            mergedVariantsMap.set(item.variantId, {...item, isExclude: false});
        });
        currentlyExcludedVariants.forEach((item) => {
            mergedVariantsMap.set(item.variantId, item);
        });
        const updatedCollectionItems = Array.from(mergedVariantsMap.values());

        const upsertCollectionData: CollectionCreateDTO = {
            name: formData.collectionName,
            collectionItems: updatedCollectionItems,
            sync: true,
            rule: removeEmptyArrays(selectedRules),
        };

        const mutationData = {
            createData: formData?.id ? undefined : upsertCollectionData,
            updateData: formData?.id ? upsertCollectionData : undefined,
        };

        const result: any = await mutateAsync(mutationData);
        clonedFormDataName.current = deepClone(formData.collectionName);
        clonedSelectedCategory.current = deepClone(collectionProductCategory?.id);

        if (isCreateAutomatedCollectionModal) {
            await setCreateAutomatedCollectionModal(false);
            navigate('/product-collection');
        }

        if (!isEmpty(result.createData?.data) && result.createData?.data) {
            await delay(2000);
            setSnackbar({
                message: `Collection ${formData.collectionName} is created.`,
                severity: 'info',
                iconName: 'Success',
            });
            resetForm();
            closeAutomatedCollectionForm(result.createData?.data);
        } else if (!isEmpty(result.updateData?.data) && result.updateData?.data) {
            setSnackbar({
                message: `Collection ${formData.collectionName} is updated.`,
                severity: 'info',
                iconName: 'Success',
            });
            setIsUpdatePending(false);
        } else {
            setSnackbar({
                message: `We cannot ${formData?.id ? 'update' : 'create'} ${
                    formData.collectionName
                } collection. Please try again later.`,
                severity: 'error',
                iconName: 'Error',
            });
            setAddAutomatedCollectionDisabled(false);
        }
    };

    const methods = useForm();

    const handleModelClose = () => {
        setCreateAutomatedCollectionModal(false);
    };

    const onCategoryClick = (category: ProductCategoryDto) => {
        excludeSelectionCheckedRef.current = false;
        setCollectionProductCategory(category);
        setSelectedRules((prev) => ({
            ...prev,
            category: [category.id],
        }));
        setIsUpdatePending(true);
    };

    const handleAutomatedCollectionSubmit = async (e: React.FormEvent) => {
        await updateExcludedVariants();
        createUpdateAutomatedCollection();
        clonedExcludedVariantIds.current = deepClone(excludedVariantIds);
        setChange(false);
        e.preventDefault();
    };

    return (
        <PageLayout testId="collections-page-layout">
            <AutomatedCollectionFormContainer>
                <Box
                    sx={{
                        width: '100%',
                        height: '100%',
                        paddingBottom: '45em',
                    }}
                    data-testid="automated-collection-form-panel"
                >
                    <FormProvider {...methods}>
                        <Box sx={{paddingBottom: '5em'}}>
                            <form
                                onSubmit={handleAutomatedCollectionSubmit}
                                data-testid="add-automated-product-collection-form"
                            >
                                <AutomatedCollectionTitleContainer>
                                    <Typography
                                        variant="largeTextStrong"
                                        data-testid="auto-collection-title"
                                    >
                                        {submitType === 'Create'
                                            ? AUTOMATED_COLLECTION_FORM_TITLE_CREATE
                                            : AUTOMATED_COLLECTION_FORM_TITLE_EDIT}
                                    </Typography>
                                    <Tooltip
                                        title={AUTOMATED_COLLECTION_FORM_TITLE_TOOLTIP}
                                        variant="multiRow"
                                        testId="tooltip"
                                        placement="top-start"
                                    >
                                        <StyledIconContainer>
                                            <Icon iconName="InfoOutlined" color="primaryBlack" />
                                        </StyledIconContainer>
                                    </Tooltip>
                                </AutomatedCollectionTitleContainer>
                                <AddAutomatedCollectionName
                                    formData={formData}
                                    setFormData={setFormData}
                                    formError={formError}
                                    setFormError={setFormError}
                                    setIsUpdatePending={setIsUpdatePending}
                                />
                                {allCategoryOptions && (
                                    <>
                                        <ProductTypeLineTitleContainer>
                                            <Typography variant="mediumTextStrong">
                                                {PRODUCT_CATEGORY_TITLE}
                                            </Typography>
                                        </ProductTypeLineTitleContainer>
                                        <ProductCategories
                                            parentCategoriesList={allCategoryOptions || []}
                                            collectionProductCategory={collectionProductCategory}
                                            onCategoryClick={onCategoryClick}
                                        />
                                    </>
                                )}
                                {collectionProductCategory &&
                                    allCategoryOptions &&
                                    attributeCategoriesData &&
                                    subCategoriesData &&
                                    brandsData && (
                                        <>
                                            <ProductCollectionRulesTitleContainer>
                                                <Typography variant="mediumTextStrong">
                                                    {PRODUCT_COLLECTION_RULES_TITLE}
                                                </Typography>
                                            </ProductCollectionRulesTitleContainer>
                                            {(!id || (id && collectionDetailsData)) && (
                                                <ProductCollectionRules
                                                    fieldName="rules"
                                                    selectedCategory={collectionProductCategory}
                                                    setSelectedRules={setSelectedRules}
                                                    collectionDetailsData={
                                                        id !== ''
                                                            ? collectionDetailsData
                                                            : undefined
                                                    }
                                                    attributeCategoriesData={
                                                        attributeCategoriesData
                                                    }
                                                    subCategoriesData={subCategoriesData}
                                                    brandsData={brandsData}
                                                    excludeSelectionCheckedRef={
                                                        excludeSelectionCheckedRef
                                                    }
                                                    setIsUpdatePending={setIsUpdatePending}
                                                />
                                            )}
                                        </>
                                    )}
                                {collectionProductCategory && (
                                    <ProductList
                                        selectedCategory={collectionProductCategory.id}
                                        selectedRules={selectedRules}
                                        excludedVariantIds={excludedVariantIds}
                                        setExcludedVariantIds={setExcludedVariantIds}
                                        excludeSelectionCheckedRef={excludeSelectionCheckedRef}
                                        collectionDetailsData={collectionDetailsData}
                                    />
                                )}
                            </form>
                        </Box>
                    </FormProvider>
                </Box>
                <CollectionFormButtons
                    handleAutomatedCollectionSubmit={handleAutomatedCollectionSubmit}
                    isAddAutomatedCollectionDisabled={isAddAutomatedCollectionDisabled}
                    closeAutomatedCollectionForm={closeAutomatedCollectionForm}
                    submitType={submitType}
                    isDeletedCollection={isDeletedCollection}
                    isUpdatePending={isUpdatePending}
                    change={change}
                />
            </AutomatedCollectionFormContainer>

            {isCreateAutomatedCollectionModal && (
                <AutomatedCollectionConfirmView
                    closeModel={handleModelClose}
                    createUpdateAutomatedCollection={createUpdateAutomatedCollection}
                    collectionName={formData.collectionName}
                />
            )}
        </PageLayout>
    );
};

export default AutomatedCollectionForm;
