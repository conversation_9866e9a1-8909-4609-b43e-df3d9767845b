include:
    - project: 'treez-inc/engineering/ci-templates/mfe'
      ref: '2.7.0'
      file: 'template.yml'

stages:
  - preconfigure
  - test
  # tag job moved earlier in the pipeline so it's run after every merge
  - tag
  - build
  - deploy
  - build_dev
  - deploy_dev
  - build_build
  - deploy_build
  - build_prod
  - deploy_prod

variables:
    # Path to the MFE. Must match the path defined in MSO Core UI
    BUCKET_PATH: product-collection/latest

install_dependencies:
    extends: .install_dependencies

lint:
    extends: .lint

unit_test:
    extends: .unit_test
    script: npm run test

build_dev:
    extends: .build_dev

deploy_dev:
    extends: .deploy_dev

build_build:
    extends: .build_build

deploy_build:
    extends: .deploy_build

tag:
    extends: .tag

build_prod:
    extends: .build_prod

deploy_prod:
    extends: .deploy_prod
