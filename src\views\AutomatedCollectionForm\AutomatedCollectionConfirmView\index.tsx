import React from 'react';
import { useNavigate } from 'react-router-dom';
import Typography from '@mui/material/Typography';
import CollectionModal from '../../../components/CollectionModal';

interface IAutomatedCollectionConfirmView {
    closeModel: () => void;
    createUpdateAutomatedCollection: () => void;
    collectionName: string;
}

const AutomatedCollectionConfirmView: React.FC<IAutomatedCollectionConfirmView> = ({
    closeModel,
    createUpdateAutomatedCollection,
    collectionName,
}) => {
    const navigate = useNavigate();
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        closeModel();
        createUpdateAutomatedCollection();
    };

    const handleClose = () => {
        closeModel();
        navigate('/product-collection');
    };

    return (
        <CollectionModal
            isOpen
            primaryButton={{
                onClick: handleSubmit,
                label: `Save & Close`,
                isDisabled: false,
            }}
            secondaryButton={{
                onClose: handleClose,
                label: 'Close without Saving',
                isDisabled: false,
            }}
            title="Unsaved Changes Detected"
            testId="create-automated-collection-modal"
        >
            <Typography>
                <b>
                    You have unsaved changes to {collectionName}. Do you want to save them
                    before leaving this screen? Any unsaved changes will be lost if you proceed without saving.
                </b>&nbsp;
            </Typography>
        </CollectionModal>
    );
};

export default AutomatedCollectionConfirmView;
