import '@testing-library/jest-dom';
import {act, renderHook} from '@testing-library/react';
import useBulkAction from '.';
import ApiService from '../../utils/apiService';
import {productListData} from '../../test/testData';
import {transformProductData} from '../../components/ProductCollectionDetailsGrid';

jest.mock('../../hooks/usePermissionsContext');
jest.mock('../../hooks/useApiContext');
jest.mock('../../hooks/useSnackbarContext');

const mockUseApiContext = require('../useApiContext').default;
const mockUseSnackbarContext = require('../useSnackbarContext').default;

const mockSetSnackbar = jest.fn();
const showConfirmation: any = jest.fn();

const validTokens = {
    accessToken: 'accessToken',
    expiresIn: 300,
    refreshToken: 'refreshToken',
    idToken: 'idToken',
};
const clearTokens = jest.fn();
const getTokens = () => validTokens;
const redirectToLogin = jest.fn();
const apiService = new ApiService(getTokens, clearTokens, redirectToLogin);

describe('useBulkAction()', () => {
    beforeEach(() => {
        jest.clearAllMocks();

        mockUseApiContext.mockImplementation(() => apiService);
        mockUseSnackbarContext.mockReturnValue({
            setSnackbar: mockSetSnackbar,
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    it('getBulkActionBarProps returns empty buttonProps array when no variants are selected', () => {
        const modifiedData: any = [];

        const {result} = renderHook(() =>
            useBulkAction(modifiedData)
        );
        const actionBar = result.current.getBulkActionBarProps(showConfirmation);

        expect(actionBar.buttonProps).toEqual([]);
        expect(actionBar.variant).toBe('primary');
    });

    it('should update selection model and total selected variants count', () => {
        const modifiedData: any = transformProductData(productListData.data);
        const {result} = renderHook(() =>
            useBulkAction(modifiedData)
        );

        act(() => {
            result.current.handleSelectionModelChange(['eef22692-3a97-4b3f-89c2-08bba6dd3ce8']);
        });

        expect(result.current.selectionModel).toEqual(['eef22692-3a97-4b3f-89c2-08bba6dd3ce8']);
        expect(result.current.totalSelectedVariants).toEqual(1);
    });

    it('should update selection model correctly when select a variant', async () => {
        const modifiedData = transformProductData(productListData.data);
        const { result } = renderHook(() => useBulkAction(modifiedData));
        
        const { handleSelectionModelChange } = result.current;

        const selectedRowId = ['eef22692-3a97-4b3f-89c2-08bba6dd3ce8'];

        await act(async () => {
            handleSelectionModelChange(selectedRowId);
        });
        

        expect(result.current.selectionModel).toEqual(selectedRowId);
    });

    it('should update the selection model correctly when unselect variants', async () => {
        const modifiedData = transformProductData(productListData.data);
        const initialSelectedRowId = [
            '7c2ea386-923e-4801-aaaf-9a5d2d4e450a', 
            'eef22692-3a97-4b3f-89c2-08bba6dd3ce8',
            'f3c64d2f-a439-48dc-9042-90f045a0916a'
        ];
        const { result } = renderHook(() => useBulkAction(modifiedData));
        const { handleSelectionModelChange } = result.current;

        act(() => {
            handleSelectionModelChange(initialSelectedRowId);
        });

        expect(result.current.selectionModel).toEqual(initialSelectedRowId);

        act(() => {
            handleSelectionModelChange(['eef22692-3a97-4b3f-89c2-08bba6dd3ce8']);
        });

        expect(result.current.selectionModel).toEqual(['eef22692-3a97-4b3f-89c2-08bba6dd3ce8']); 
    });

});
