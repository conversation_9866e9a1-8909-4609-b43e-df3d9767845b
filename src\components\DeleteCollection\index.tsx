import Typography from '@mui/material/Typography';
import React from 'react';

interface IDeleteCollection {
    name: string;
    testid: string;
}

const DeleteCollection: React.FC<IDeleteCollection> = ({name, testid}) => (
    <Typography data-testid={testid}>
        Are you sure you want to delete the collection&nbsp;
        <b>{name}?</b>&nbsp;
    </Typography>
);

export default DeleteCollection;
