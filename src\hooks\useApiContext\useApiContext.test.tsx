import {renderHook, waitFor} from '@testing-library/react';
import React from 'react';
import useApiContext from '.';
import ApiProvider from '../../providers/ApiProvider';
import ApiService from '../../utils/apiService';

describe('useApiContext()', () => {
    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    it('should throw the expected error when called outside an API provider', async () => {
        const expectedError = new Error('useApiContext must be used within an ApiProvider');

        await renderHook(() => {
            try {
                useApiContext();
            } catch (e) {
                expect(e as Error).toEqual(expectedError);
            }
        });
    });

    it('should return the API context when called within an API provider', async () => {
        const mockApi = jest.fn() as unknown as ApiService;
        const wrapper = ({children}: {children: React.ReactNode}) => (
            <ApiProvider api={mockApi}>{children}</ApiProvider>
        );

        const {result} = await renderHook(() => useApiContext(), {
            wrapper,
        });

        await waitFor(() => {
            expect(result.current).toEqual({
                api: mockApi,
            });
        });
    });
});
