import React from 'react';
import '@testing-library/jest-dom';
import {fireEvent, render, waitFor} from '@testing-library/react';
import {createMemoryHistory, MemoryHistory} from 'history';
import {TreezThemeProvider} from '@treez-inc/component-library';
import ErrorNotFound from '.';

const mockedUseNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
    useNavigate: () => mockedUseNavigate,
}));

const testIdContainer = 'error-not-found';
const testIdErrorTemplate = 'error-template';
const testIdErrorTitle = 'error-template-error-title';
const testIdBodyWrapper = 'error-template-body-wrapper';

let mockHistory: MemoryHistory;

beforeEach(() => {
    mockHistory = createMemoryHistory();
});

afterEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
    jest.resetAllMocks();
});

describe('<ErrorNotFound />', () => {
    const renderNoPermissionComponent = () => {
        const {getByTestId, getByRole} = render(
            <TreezThemeProvider>
                <ErrorNotFound />
            </TreezThemeProvider>
        );
        const container = getByTestId(testIdContainer);
        const errorTemplate = getByTestId(testIdErrorTemplate);
        const errorTitle = getByTestId(testIdErrorTitle);
        const bodyWrapper = getByTestId(testIdBodyWrapper);
        const button = getByRole('button');

        return {
            container,
            errorTemplate,
            errorTitle,
            bodyWrapper,
            button,
        };
    };

    it('renders error template with correct text', () => {
        const {container, errorTitle, bodyWrapper, button} = renderNoPermissionComponent();

        expect(container).toBeInTheDocument();
        expect(errorTitle).toHaveTextContent("This page doesn't exist!");
        expect(bodyWrapper).toHaveTextContent("Let's try one of these instead:");
        expect(button).toHaveTextContent('Home');
    });

    it('navigates to dashboard on button click', async () => {
        const {button} = renderNoPermissionComponent();

        await waitFor(() => {
            fireEvent.click(button);
            expect(mockHistory.location.pathname).toEqual('/');
        });
    });
});
