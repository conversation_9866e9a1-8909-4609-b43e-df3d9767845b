import { CollectionItems } from '../../interfaces/collectionForm';
import IProductCollection from '../../interfaces/productCollection';
import {IProductCollectionData} from '../../interfaces/productCollectionResponse';

const checkActiveCollectionNameExist = (
    productCollections: IProductCollectionData[],
    collectionName: string | undefined,
    existingCollectionName: string | undefined,
) => {
    if (productCollections && productCollections.length > 0) {
        return productCollections.some(
            (collection: IProductCollectionData) =>
                collection.deletedAt === null &&
                collection.name.trim().toLowerCase() === collectionName?.trim().toLowerCase() &&
                collectionName?.trim().toLowerCase() !== existingCollectionName?.trim().toLowerCase()
        );
    }
    return false;
};

const checkCollectionNameLength = (collectionName: string) => {
    if (collectionName.length <= 64) {
        return false;
    }
    return true;
};

const collectionNameLengthIsEmpty = (collectionName: string) => {
    if (collectionName.length < 1) {
        return true;
    }
    return false;
};

const isManualCollection = (collection: IProductCollection) => {
    if (collection.sync === false) return true;
    return false;
};

const collectionFormErrorMessage = (
    isNameEmpty: boolean,
    isMaxLimitReached: boolean,
    isCollectionNameExists: boolean
): string => {
    if (isNameEmpty) {
        return "Name can't be empty";
    }
    if (isCollectionNameExists) {
        return 'Name is already in use, Choose a different name';
    }
    if (isMaxLimitReached) {
        return 'Name should be 64 characters or less';
    }
    return ''; // Default return value if none of the conditions are met
};

const mergeExcludedVariants = (
    existingCollectionItems: CollectionItems[] | undefined,
    excludedVariantIds: string[] | undefined
): CollectionItems[] => {
    const mergedVariantsMap = new Map<string, CollectionItems>();

    const existingExcludedVariants = existingCollectionItems?.map((item) => ({
        variantId: item.variantId,
        isExclude: item.isExclude,
    })) ?? [];

    const currentlyExcludedVariants = excludedVariantIds?.map((variantId) => ({
        variantId,
        isExclude: true,
    })) ?? [];

    existingExcludedVariants.forEach((item) =>
        mergedVariantsMap.set(item.variantId, { ...item, isExclude: false })
    );

    currentlyExcludedVariants.forEach((item) =>
        mergedVariantsMap.set(item.variantId, item)
    );

    return Array.from(mergedVariantsMap.values());
};

export default {
    checkActiveCollectionNameExist,
    checkCollectionNameLength,
    collectionNameLengthIsEmpty,
    isManualCollection,
    collectionFormErrorMessage,
    mergeExcludedVariants,
};
