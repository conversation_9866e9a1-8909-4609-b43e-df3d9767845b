import {styled} from '@mui/material/styles';
import {convertPxToRem} from '@treez-inc/component-library';
import React from 'react';

const GridContainer = styled('div')(({theme}) => ({
    height: '100%',
    background: theme.palette.primaryWhite.main,
    paddingRight: convertPxToRem(52),
    paddingLeft: convertPxToRem(52),
    paddingTop: convertPxToRem(52),
}));

export interface GridLayoutProps {
    children: React.ReactNode;
    testId: string;
}

const GridLayout: React.FC<GridLayoutProps> = ({children, testId}: GridLayoutProps) => (
    <GridContainer data-testid={testId}>{children}</GridContainer>
);

export default GridLayout;
