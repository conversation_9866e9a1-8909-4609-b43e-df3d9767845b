import {AxiosInstance} from 'axios';
import retryRequest from '.';

const request = jest.fn();
const toJSON = jest.fn();

describe('Retry Request', () => {
    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    it('calls api request with config', () => {
        toJSON.mockReturnValue('serializedHeaders');
        const config = {url: 'url', method: 'get'};
        const api = {request} as unknown as AxiosInstance;
        const headers = {toJSON};

        retryRequest(config, api, headers);

        expect(api.request).toBeCalledTimes(1);
        expect(api.request).toBeCalledWith({
            ...config,
            headers: 'serializedHeaders',
        });
    });
});
