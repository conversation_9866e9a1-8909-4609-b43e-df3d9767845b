import React, {useEffect, useState} from 'react';
import {styled, Box, SelectChangeEvent} from '@mui/material/';
import {useFormContext, UseFieldArrayRemove, Controller} from 'react-hook-form';
import {IconButton, convertPxToRem} from '@treez-inc/component-library';
import HookFormSelect from '../../../../hooks/forms/HookFormSelect';
import MultiSelectSearch, {MenuItemsProps} from '../../../../components/Shared/MultiSelectSearch';
import {AutomatedCollectionRulesForm} from '../../../../interfaces/attributeCategoryData';

const AdditionalFilterCompContainer = styled(Box)({
    display: 'flex',
    flexDirection: 'row',
    columnGap: convertPxToRem(10),
});

interface AdditionalFilterComponentProps {
    fieldName: string;
    index: number;
    remove: UseFieldArrayRemove;
    attributeCategories: AutomatedCollectionRulesForm[];
    onMultiSelectChange: (ruleName: any, newValue: string[]) => void;
    selectedRuleFilters: string[];
    selectedRuleCategory: string;
    selectedMenuItems: any;
    removeAddedRulesFilter: (ruleName: string) => void;
}

const AdditionalFilterComponent = ({
    fieldName,
    index,
    remove,
    attributeCategories,
    onMultiSelectChange,
    selectedRuleFilters,
    selectedRuleCategory,
    selectedMenuItems,
    removeAddedRulesFilter,
}: AdditionalFilterComponentProps) => {
    const [selectedRule, setSelectedRule] = useState<AutomatedCollectionRulesForm | null>(null);
    const [menuItems, setMenuItems] = useState<MenuItemsProps[]>([]);
    const [isFilterSelected, setFilterSelection] = useState<boolean>(false);

    const handleCategoryChange = (event: SelectChangeEvent<unknown>) => {
        const selectedId = event.target.value as string;
        const selectedCategory = attributeCategories?.find(
            (category) => category.name === selectedId
        );
        if (selectedCategory) {
            /* eslint-disable no-param-reassign */
            selectedRuleCategory = selectedCategory.name;
            /* eslint-enable no-param-reassign */
            setSelectedRule(selectedCategory);
            const newMenuItems =
                selectedCategory.options
                    ?.map((attribute) => ({
                        displayName: attribute.name,
                        displayValue: attribute.id,
                    }))
                    .sort((a, b) => a.displayName.localeCompare(b.displayName)) || [];
            setMenuItems(newMenuItems);
        } else {
            setMenuItems([]);
        }
    };

    const {control, getValues} = useFormContext();

    useEffect(() => {
        setFilterSelection(getValues(selectedRuleCategory)?.length > 0);
    }, [selectedRuleCategory]);

    return (
        <AdditionalFilterCompContainer>
            <Box sx={{width: '100%'}}>
                {attributeCategories && (
                    <HookFormSelect
                        disabled={isFilterSelected}
                        name={`${fieldName}.${index}`}
                        label="Filter"
                        menuItems={attributeCategories
                            .filter((item) => item.active)
                            .map((category) => ({
                                displayName: category.name,
                                displayValue: category.name,
                            }))}
                        selectedMenuItems={selectedRuleFilters}
                        onChange={handleCategoryChange}
                    />
                )}
            </Box>
            <Box sx={{width: '100%'}}>
                {attributeCategories && (
                    <Controller
                        key={`${selectedRuleCategory || selectedRule?.name}`}
                        name={`${selectedRuleCategory || selectedRule?.name}`}
                        control={control}
                        render={({field: {onChange, onBlur, value, ref}}) => (
                            <MultiSelectSearch
                                onChange={(ruleValue: any) => {
                                    onMultiSelectChange(
                                        selectedRuleCategory || selectedRule?.name,
                                        ruleValue
                                    );
                                    onChange(ruleValue);
                                    setFilterSelection(true);
                                }}
                                onBlur={onBlur}
                                value={value || []}
                                ref={ref}
                                menuItems={selectedMenuItems || menuItems || []}
                                label="Tags"
                                options={{
                                    createNewOption: selectedRule?.custom,
                                    parentOption: selectedRuleCategory || selectedRule?.name,
                                }}
                            />
                        )}
                    />
                )}
            </Box>
            <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
                <IconButton
                    disabled={!selectedRuleCategory}
                    iconName="Delete"
                    variant="secondary"
                    onClick={() => {
                        remove(index);
                        removeAddedRulesFilter(selectedRuleCategory);
                    }}
                    size="medium"
                />
            </Box>
        </AdditionalFilterCompContainer>
    );
};

export default AdditionalFilterComponent;
