enum LOG_LEVEL {
    debug = 0,
    info = 1,
    error = 2,
}

type LogLevelKey = keyof typeof LOG_LEVEL;

const debug = process.env.CYPRESS_DEBUG;

const logLevelKey = debug === 'true' ? 'debug' : 'info';
console.info(`Using log level: ${logLevelKey}`);

const log = (key: LogLevelKey, message: string) => {
    if (LOG_LEVEL[key] >= LOG_LEVEL[logLevelKey]) {
        console[key](message);
    }
};

export const logger = {
    debug: (message: string) => log('debug', message),
    info: (message: string) => log('info', message),
    error: (message: string) => log('error', message),
};
