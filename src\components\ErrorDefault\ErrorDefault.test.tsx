import React from 'react';
import '@testing-library/jest-dom';
import {fireEvent, render, waitFor} from '@testing-library/react';
import {TreezThemeProvider} from '@treez-inc/component-library';
import ErrorDefault from '.';

const reload = jest.fn();

jest.spyOn(window, 'location', 'get').mockImplementation(() => ({reload} as unknown as Location));

const testIdContainer = 'error-default';
const testIdErrorTemplate = 'error-template';
const testIdErrorTitle = 'error-template-error-title';
const testIdBodyWrapper = 'error-template-body-wrapper';

describe('<ErrorDefault />', () => {
    const renderErrorDefaultComponent = () => {
        const {getByTestId, getByRole} = render(
            <TreezThemeProvider>
                <ErrorDefault />
            </TreezThemeProvider>
        );
        const container = getByTestId(testIdContainer);
        const errorTemplate = getByTestId(testIdErrorTemplate);
        const errorTitle = getByTestId(testIdErrorTitle);
        const bodyWrapper = getByTestId(testIdBodyWrapper);
        const button = getByRole('button');

        return {
            container,
            errorTemplate,
            errorTitle,
            bodyWrapper,
            button,
        };
    };

    it('renders error template with correct text', () => {
        const {container, errorTitle, bodyWrapper, button} = renderErrorDefaultComponent();

        expect(container).toBeInTheDocument();
        expect(errorTitle).toHaveTextContent('Hmm… Something went wrong');
        expect(bodyWrapper).toHaveTextContent(
            "We're having trouble loading this page. Refresh to try again. If the issue persists please contact Customer Support."
        );
        expect(button).toHaveTextContent('Refresh');
    });

    it('reloads page on button click', async () => {
        const {button} = renderErrorDefaultComponent();

        await waitFor(() => {
            fireEvent.click(button);
            expect(reload).toBeCalledTimes(1);
        });
    });
});
