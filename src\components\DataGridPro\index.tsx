/* eslint-disable react/destructuring-assignment */
import React from 'react';
import { DataGridPro as MUIDataGridPro, DataGridProProps as MUIDataGridProProps } from '@mui/x-data-grid-pro';
import { styled } from '@mui/material';
import { Icon, convertPxToRem } from '@treez-inc/component-library';
import { DataGridProLoadingOverlay } from './DataGridProComponentOverrides/DataGridProLoadingOverlay';
import DataGridProToolBar from './DataGridProComponentOverrides/DataGridProToolBar';
import DataGridProPagination from './DataGridProComponentOverrides/DataGridProPagination';

// prevent paginationModel prop from being passed in so this component always has control over
//    1. the # of rows per page
//    2. landing the user on the 1st page of results on initial render
interface DataGridProProps extends Omit<MUIDataGridProProps, 'paginationModel'> {
    rowCount: number;
}

export const StyledDataGridPro = styled(MUIDataGridPro)<MUIDataGridProProps>(
    ({ theme, loading = false, rows, columns }) => ({
        color: theme.palette.secondaryText.main,
        border: 'none',
        // weird MUI behavior never hides the select all checkbox when the grid  is loading or empty
        '& .MuiDataGrid-columnHeaderCheckbox': {
            display: loading || !rows.length || !columns.length ? 'none' : '',
        },
        '& .MuiDataGrid-columnHeaders': {
            backgroundColor: theme.palette.primaryWhite,
        },
        '& .MuiDataGrid-columnHeader': {
            ':focus': {
                outline: 'none',
            },
            ':focus-within': {
                outline: 'none',
            },
        },
        '& .MuiDataGrid-cell': {
            ':focus': {
                outline: 'none',
            },
            ':focus-within': {
                outline: 'none',
            },
        },
        '& .MuiDataGrid-row': {
            backgroundColor: theme.palette.primaryWhite,
            '&.Mui-selected': {
                backgroundColor: theme.palette.treezGreen[3],
            },
            '&:hover': {
                backgroundColor: theme.palette.treezGrey[2],
            },
        },
        '& .DataGrid-Row-Child': {
            backgroundColor: theme.palette.grey01.main,
        },
        '& .MuiDataGrid-columnSeparator': {
            color: theme.palette.treezGrey[4],
        },
        '& .MuiDataGrid-footerContainer': {
            padding: `${convertPxToRem(8)} ${convertPxToRem(12)}`,
        },
        // pagination component
        '& .MuiPaginationItem-root': {
            color: theme.palette.primaryBlack.main,
            ...theme.typography.mediumText,
            '&.Mui-selected': {
                backgroundColor: theme.palette.treezGreen[4],
                color: theme.palette.text.primary,
                ...theme.typography.mediumTextStrong,
            },
            '&.Mui-disabled': {
                color: theme.palette.disabledText.main,
            },
            '&:hover': {
                color: theme.palette.secondaryText.main,
                textDecoration: 'underline',
                ...theme.typography.mediumText,
            },
        },
        '& .MuiPagination-ul': {
            'li:first-of-type': {
                marginRight: convertPxToRem(20),
            },
            'li:last-of-type': {
                marginLeft: convertPxToRem(20),
            },
        },

        // MUI automatically displays row count on ssr, but our designs don't need that
        '& .MuiTablePagination-displayedRows': {
            display: 'none',
        },
    }),
);

// object with icon overrides to be spread out in the DataGridPro's components prop
export const DataGridProIconOverrides = {
    columnSortedAscendingIcon: () => <Icon iconName="ArrowDropup" />,
    columnSortedDescendingIcon: () => <Icon iconName="ArrowDropdown" />,
    detailPanelCollapseIcon: () => <Icon iconName="ChevronDown" />,
    detailPanelExpandIcon: () => <Icon iconName="ChevronRight" />,
    quickFilterIcon: () => <Icon iconName="Search" />,
};

export const DataGridPro = (props: DataGridProProps) => {
    const { pagination, paginationMode, rowCount = 0 } = props;

    // Only fallback to internal logic if `pagination` is undefined
    const shouldPaginate = typeof pagination === 'boolean' ? pagination : rowCount > 20;

    let resolvedPaginationMode: 'client' | 'server' | undefined;

    if (typeof paginationMode !== 'undefined') {
    resolvedPaginationMode = paginationMode;
    } else if (shouldPaginate) {
    resolvedPaginationMode = 'server';
    }

    return (
        <StyledDataGridPro
            {...props}
            disableColumnMenu
            disableRowSelectionOnClick
            pagination={shouldPaginate}
            paginationMode={resolvedPaginationMode}
            hideFooterRowCount={!shouldPaginate}
            initialState={{
                ...props.initialState,
                pagination: {
                    paginationModel: {
                        pageSize: 20,
                    },
                },
            }}
            pageSizeOptions={[20]}
            slots={{
                ...props.slots,
                ...DataGridProIconOverrides,
                loadingOverlay: DataGridProLoadingOverlay,
                toolbar: DataGridProToolBar,
                pagination: DataGridProPagination,
            }}
        />
    );
};