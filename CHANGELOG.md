## [1.9.3] - 2025-06-24
See merge request treez-inc/engineering/back-of-house/product-collections-micro-front-end!86

## Description
### Added

* Added back removed code from the last [MR](https://gitlab.com/treez-inc/engineering/back-of-house/product-collections-micro-front-end/-/commit/cbbb00e4692552616c54d1742fa836e61deb06ce)

### Close Issues

Closes #133

### Personal
Co-Authors

Merged By
<PERSON> <<EMAIL>>

Approvers
Approved-by: <PERSON> <<EMAIL>>


## [1.9.2] - 2025-06-24
See merge request treez-inc/engineering/back-of-house/product-collections-micro-front-end!85

## Description
### Fixed

* Filtering through the dropdown menu was not working reliably due to the large number of items (14,858). To fix this, I added a debounce to delay the filtering until the user pauses typing, and limited the filtered results to the first 100 matches using .slice(). This improves performance and ensures the UI remains responsive during search.

### Close Issues

Closes #132

### Personal
Co-Authors

Merged By
<PERSON> <<EMAIL>>

Approvers
Approved-by: <PERSON>iraldelli <<EMAIL>>


## [1.9.1] - 2025-06-18
See merge request treez-inc/engineering/back-of-house/product-collections-micro-front-end!84

## Description
### Fixed

* Background colors for when selecting and deselecting a row within the DataGrid.

### Close Issues

Closes #128

### Personal
Co-Authors
Co-authored-by: Lucas Giraldelli <<EMAIL>>

Merged By
Miguel Ganoza <<EMAIL>>

Approvers
Approved-by: Andrew Bishop <<EMAIL>>


## [1.9.0] - 2025-06-12
See merge request treez-inc/engineering/back-of-house/product-collections-micro-front-end!83

## Description
Post Deploy stages disabled in sandbox and dev to prevent persistent QA permissions issues

### Removed
* Cypress tests in CI. They are not providing enough value to justify the maintenance burden

### Fixed
* CI pipeline failures due to QA user permissions


### Close Issues

Closes #125

### Personal
Co-Authors

Merged By
Andrew Bishop <<EMAIL>>

Approvers
Approved-by: Lucas Giraldelli <<EMAIL>>
Approved-by: Leo Belanger <<EMAIL>>


## [1.8.0] - 2025-06-11
See merge request treez-inc/engineering/back-of-house/product-collections-micro-front-end!82

## Description
### Added 

* Added Persist Filters and Exclusions When Switching Product Categories in Collections

### Close Issues

Closes #121

### Personal
Co-Authors

Merged By
Miguel Ganoza <<EMAIL>>

Approvers
Approved-by: Lucas Giraldelli <<EMAIL>>
Approved-by: Andrew Bishop <<EMAIL>>


## [1.7.0] - 2025-06-04
See merge request treez-inc/engineering/back-of-house/product-collections-micro-front-end!81

## Description
Description goes here

### Added

* Implemented filtering for Product Collection Rules. Selecting a filter dynamically updates the dropdown menu to exclude already selected items.

### Close Issues

Closes #120

### Personal
Co-Authors

Merged By
Miguel Ganoza <<EMAIL>>

Approvers
Approved-by: Andrew Bishop <<EMAIL>>


## [1.6.2] - 2025-05-30
See merge request treez-inc/engineering/back-of-house/product-collections-micro-front-end!80

## Description
Move tag job earlier in the pipeline (after the tests step) on merge commits, so tag is automatically cut on merges to main and redundant jobs are removed.

### Changed
* gitlab pipeline step ordering

### Close Issues

Closes #119

### Personal
Co-Authors

Merged By
Andrew Bishop <<EMAIL>>

Approvers
Approved-by: Lucas Giraldelli <<EMAIL>>
Approved-by: Leo Belanger <<EMAIL>>


## [1.6.1] - 2025-05-29
See merge request treez-inc/engineering/back-of-house/product-collections-micro-front-end!79

## Description
### Added

* When creating a new collection rule filter it will trigger the save button

### Fixed

* When there is a filter in place and no products are displayed the number of excluded should always be 0

### Close Issues

Closes #122

### Personal
Co-Authors

Merged By
Miguel Ganoza <<EMAIL>>

Approvers
Approved-by: Lucas Giraldelli <<EMAIL>>
Approved-by: Andrew Bishop <<EMAIL>>


## [1.6.0] - 2025-05-27
See merge request treez-inc/engineering/back-of-house/product-collections-micro-front-end!78

## Description
Description goes here

### Added

* Added check box in the Grid Header for when selected it checks and unchecks all items.
* When there is no products the search bar will be removed to not allow searching same with the pagination
* The save button is not enabled when you change the name, select products to exclude or change the category otherwise if no changes are made button will be disabled
* if you make changes and try press the cancel button a modal will pop up asking the user if they want to save before leaving that page. 

### Changed

* Save and Modal Text.
* Confirmation Modal.
* page now scrolls down instead of the grid

### Removed

* Removed Breadcrumbs header
* Removed border around form

### Close Issues

Closes #118

### Personal
Co-Authors
Co-authored-by: Andrew Bishop <<EMAIL>>

Merged By
Miguel Ganoza <<EMAIL>>

Approvers
Approved-by: Chase Jepson <<EMAIL>>
Approved-by: Jeremy Culler <<EMAIL>>


## [1.5.0] - 2025-01-10
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!74

## Description
MerchandiseType in automated collection rule 

### Added
* MerchandiseType in automated collection rule 

### Close Issues
#101

### Personal
Co-Authors

Merged By
Raghul R Nair <<EMAIL>>

Approvers
Approved-by: Pranoy Sebastian <<EMAIL>>
Approved-by: Aparna Gurumurthy <<EMAIL>>


## [1.4.0] - 2024-12-05
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!73

## Description
When user clicks to Create collection, show the popup to proceed with what type of collection they wanted to created, then provide the placeholder to type the collection name and navigate to the Manual/Automated collection page.

### Added
* Added auto saving of excluded variants in automated product collection

### Changed
* Changed product collection creation workflow of both manual and automated collection
* Upgraded component library

### Fixed
* Fixed excluded list getting cleared after the filter changes

### Close Issues
#103 

Closes #103

### Personal
Co-Authors

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Aparna Gurumurthy <<EMAIL>>
Approved-by: Jishnu Mohan <<EMAIL>>


## [1.3.1] - 2024-09-05
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!71

## Description
cypress Timeout update


### Changed
* cypress defaultCommandTimeout to 10000
 

### Close Issues

### Personal
Co-Authors
Co-authored-by: Raghul <<EMAIL>>

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Pranoy Sebastian <<EMAIL>>
Approved-by: Jishnu Mohan <<EMAIL>>


## [1.3.0] - 2024-08-28
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!67

## Description
Refractor "Size" in product collections to "Amount

### Closed Issues
[94](https://gitlab.com/treez-inc/engineering/front-of-house/product-collections-micro-front-end/-/issues/94)

### Added
Added new variant model changes

### Changed
Changed Size to Amount in product collections rules

### Deleted
Removed variantProperties

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Jishnu Mohan <<EMAIL>>
Approved-by: Aparna Gurumurthy <<EMAIL>>


## [1.2.5] - 2024-06-28
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!65

## Description
Inconsistent behavior displaying saved changes after reopening product collection and Filters don't display at all until the page is refreshed
### Closed Issues
#90 

### Fixed
Inconsistent behavior displaying saved changes after reopening product collection
Filters don't display at all until the page is refreshed


## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Hope Le <<EMAIL>>
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Raghul R Nair <<EMAIL>>


## [1.2.4] - 2024-06-07
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!63

## Description
Some selected excluded variants are not getting added while automated collection creation
### Closed Issues
#88 


### Fixed
Some selected excluded variants are not getting added while automated collection creation


## MR Requirements
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>
Approved-by: Aparna Gurumurthy <<EMAIL>>


## [1.2.3] - 2024-06-05
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!62

## Description
Product Header Labels jump around awkwardly when switching between catagories.
### Closed Issues
#84 #86 #87 

### Fixed
Resolved Product Header Labels jump around awkwardly when switching between catagories.

## MR Requirements
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Aparna Gurumurthy <<EMAIL>>
Approved-by: Jishnu Mohan <<EMAIL>>


## [1.2.2] - 2024-06-05
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!61

## Description
Sending empty rule filter during automated product collection create/update
### Closed Issues
#85 

### Fixed
Removed empty rule filter during automated product collection create/update

## MR Requirements
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Jishnu Mohan <<EMAIL>>
Approved-by: Jeremy Culler <<EMAIL>>


## [1.2.1] - 2024-05-28
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!60

## Description
Typing in filter rules doesn't work in automated product collection
### Closed Issues
#82 

### Fixed
Typing in rules filter doesn't work in automated product collection


## MR Requirements
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Aparna Gurumurthy <<EMAIL>>
Approved-by: Jishnu Mohan <<EMAIL>>


## [1.2.0] - 2024-05-24
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!59

## Description
Enabled size filter with other uom options in automated collection

### Closed Issues
#67

### Changed
Enabled size filter with other uom options in automated collection


## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Aparna Gurumurthy <<EMAIL>>
Approved-by: Jishnu Mohan <<EMAIL>>


## [1.1.5] - 2024-05-22
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!57

## Description
Description goes here

### Changed
Changed order of types on type picker

### Close Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/262

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>
Approved-by: Hope Le <<EMAIL>>


## [1.1.4] - 2024-05-22
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!54

## Description
Show Excluded variants in the automated collection pagination
### Closed Issues
#72 
#73

### Fixed
Resolved excluded list in the automated collection pagination

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Dan Hill <<EMAIL>>


## [1.1.3] - 2024-05-21
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!56

## Description

### Fixed
* Subcategory filter sometimes loading improperly.


### Close Issues
https://gitlab.com/treez-inc/engineering/front-of-house/product-collections-micro-front-end/-/issues/77

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>
Approved-by: Prudhvi Athuru <<EMAIL>>


## [1.1.2] - 2024-05-21
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!55

## Description
### Fixed
* Inconsistent loading of type and rule data on product collections


### Security 
* in case of vulnerabilities.

### Close Issues

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>
Approved-by: Prudhvi Athuru <<EMAIL>>


## [1.1.1] - 2024-05-16
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!52

## Description
no name validation error 
 
### Fixed
* name validation error 


### Close Issues
#75

### Personal
Co-Authors
Co-authored-by: Jishnu Mohan <<EMAIL>>

Merged By
Raghul R Nair <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Aparna Gurumurthy <<EMAIL>>


## [1.1.0] - 2024-05-10
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!50

## Description
Add global product collection selection
### Closed Issues
#57

### Added
Added global product collection selection

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Raghul R Nair <<EMAIL>>
Approved-by: Aparna Gurumurthy <<EMAIL>>


## [1.0.3] - 2024-05-07
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!47

## Description
Link to add products to existing manual collection from collections page

### Added
* Link to product page.
* Refresh button.


### Close Issues

Closes #64

### Personal
Co-Authors

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: Wade Hastings <<EMAIL>>
Approved-by: Hope Le <<EMAIL>>


## [1.0.2] - 2024-05-07
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!49

## Description
Newly added automated collection doesn't immediately display in the list
### Closed Issues
#62 

### Fixed
Newly added automated collection doesn't immediately display in the list


## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors
Co-authored-by: Raissa Bergamini <<EMAIL>>

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Aparna Gurumurthy <<EMAIL>>
Approved-by: Jishnu Mohan <<EMAIL>>


## [1.0.1] - 2024-05-07
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!46

## Description
Add Collection Type Column

### Added
* Collection type.


### Close Issues

Closes #59

### Personal
Co-Authors

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Hope Le <<EMAIL>>


## [1.0.0] - 2024-05-07
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!34

## Description
Automated product collection

### Added
* Added automated product collection

### Personal
Co-Authors
Co-authored-by: Raissa Bergamini <<EMAIL>>

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Jishnu Mohan <<EMAIL>>
Approved-by: Aparna Gurumurthy <<EMAIL>>


## [0.14.0] - 2024-03-04
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!30

## Description
Add Loader to Write Actions
### Closed Issues
#47 

<!-- Be sure to delete unused headers and comments -->
### Added
Added loader spinner to product collections

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Aparna Gurumurthy <<EMAIL>>
Approved-by: Ankita Jain <<EMAIL>>


## [0.13.0] - 2024-03-01
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!31

## Description
### Closed Issues

#50 

### Changed

Updated confirmation message in remove variants Modal

### 

## MR Requirements

- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [ ] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Ankita Jain <<EMAIL>>

Approvers
Approved-by: Aparna Gurumurthy <<EMAIL>>
Approved-by: Pranoy Sebastian <<EMAIL>>


## [0.12.1] - 2024-02-28
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!29

## Description
Ability to remove 0 products from product collection
### Closed Issues
#48 

### Fixed
Fixed the ability to remove 0 products from product collection

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Ankita Jain <<EMAIL>>
Approved-by: Aparna Gurumurthy <<EMAIL>>


## [0.12.0] - 2024-02-27
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!27

## Description
Navigate to collection detailed page after the collection create
### Closed Issues
#45 

### Added
Navigate to detailed page after the collection create

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Aparna Gurumurthy <<EMAIL>>
Approved-by: Raghul R Nair <<EMAIL>>
Approved-by: Ankita Jain <<EMAIL>>


## [0.11.0] - 2024-02-23
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!26

## Description
### Closed Issues #44 

### Added

### Changed

* Enable creating product collection with the same name as a deleted collection

### Fixed

### Deleted

## MR Requirements

- [ ] ~~If you made visual changes, you have completed the design review process and your changes have been approved~~
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [ ] ~~You added a demo video or a screenshot for any visual changes to a comment on the MR~~
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jishnu Mohan <<EMAIL>>

Approvers
Approved-by: Ankita Jain <<EMAIL>>
Approved-by: Raghul R Nair <<EMAIL>>
Approved-by: Aparna Gurumurthy <<EMAIL>>


## [0.10.1] - 2024-02-09
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!23

## Description
Enhance unit test coverage

### Closed Issues
#40 

### Added
Enhanced unit test coverage and fixed the code format

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [ ] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [ ] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Ankita Jain <<EMAIL>>
Approved-by: Raghul R Nair <<EMAIL>>


## [0.10.0] - 2024-02-07
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!22

## Description
<!-- Add high-level description here -->
### Closed Issues
#39 

<!-- Be sure to delete unused headers and comments -->

### Changed
Removed inline style and replaced them with Style component.

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [ ] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Ankita Jain <<EMAIL>>

Approvers
Approved-by: Pranoy Sebastian <<EMAIL>>
Approved-by: Aparna Gurumurthy <<EMAIL>>


## [0.9.2] - 2024-02-02
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!21

## Description
Production build script

### Added
* Production build script `build:prod`

### Close Issues
#33

### Personal
Co-Authors

Merged By
Raghul R Nair <<EMAIL>>

Approvers
Approved-by: Pranoy Sebastian <<EMAIL>>
Approved-by: Jishnu Mohan <<EMAIL>>


## [0.9.1] - 2024-02-02
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!20

## Description
Pagination issue in product collection detailed view
### Closed Issues
#37 

### Fixed
Fixed pagination issue in product collection detailed view

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Raghul R Nair <<EMAIL>>
Approved-by: Ankita Jain <<EMAIL>>


## [0.9.0] - 2024-01-31
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!19

## Description
Renamed Deactivate text to Delete
### Closed Issues
#36 

### Changed
Renamed Deactivate text to Delete

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Aparna Gurumurthy <<EMAIL>>
Approved-by: Raghul R Nair <<EMAIL>>


## [0.8.1] - 2024-01-31
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!16

## Description
Added manage permission for 'Add Collection' button
### Closed Issues
#35 

### Fixed
Fixed bug for adding manage permission for 'Add Collection' button

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Aparna Gurumurthy <<EMAIL>>
Approved-by: Raghul R Nair <<EMAIL>>


## [0.8.0] - 2024-01-24
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!14

## Description
As a Dispensary User, user should able to Remove the product from the filtered list, so that It will be easy for them to Remove all the products/certain product(s).

### Closed Issues
#23 

<!-- Be sure to delete unused headers and comments -->
### Added
Added variants remove from the product collection

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Aparna Gurumurthy <<EMAIL>>
Approved-by: Raghul R Nair <<EMAIL>>


## [0.7.0] - 2024-01-18
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!13

## Description
<!-- Add high-level description here -->
### Closed Issues
#24 

<!-- Be sure to delete unused headers and comments -->
### Added
Added template for the empty grid in Product collection detail page

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Ankita Jain <<EMAIL>>

Approvers
Approved-by: Aparna Gurumurthy <<EMAIL>>
Approved-by: Pranoy Sebastian <<EMAIL>>


## [0.6.0] - 2024-01-09
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!10

## Description
As a Dispensary User, user should able to Remove the product from the filtered list, so that It will be easy for them to Remove all the products/certain product(s).

### Closed Issues
#13 


### Added
Implemented remove variants from product collection

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Raghul R Nair <<EMAIL>>
Approved-by: Aparna Gurumurthy <<EMAIL>>


## [0.5.0] - 2024-01-05
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!11

## Description

### Closed Issues 
#21 

### Fixed
Updated Filter status for collection list

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [ ] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [ ] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [ ] Your changes meet all of the Acceptance Criteria listed in the user story
- [ ] You reviewed your own MR and left comments where needed to provide context for reviewers
- [ ] You added unit tests to cover your changes
- [ ] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Ankita Jain <<EMAIL>>

Approvers
Approved-by: Pranoy Sebastian <<EMAIL>>
Approved-by: Aparna Gurumurthy <<EMAIL>>


## [0.4.0] - 2024-01-05
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!8

## Description
As a dispensary user, I want to view the product and variants details that is available under each product collection.
### Closed Issues
#20 

### Added
Added product collection detail view

### Changed
Enabled click event for each row in collection gridview

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors
Co-authored-by: Raghul <<EMAIL>>

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Stacey Sugiono <<EMAIL>>
Approved-by: Aparna Gurumurthy <<EMAIL>>


## [0.3.0] - 2023-12-07
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!7

## Description
As a dispensary user, User should have an option to delete the existing product collection via Manage collection screen, so that User will have an ease to delete the product collection at a go.

### Added
- Added the Fly-out menu for delete option for each row in product collection
- Added product collection deactivation.

### Changed
Updated product collection list.

### Closed Issues
#15

### Personal
Co-Authors

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Raghul R Nair <<EMAIL>>
Approved-by: Aparna Gurumurthy <<EMAIL>>


## [0.1.0] - 2023-11-17
See merge request treez-inc/engineering/front-of-house/product-collections-micro-front-end!4

## Description
Implement permissions for the Product Collections.
### Closed Issues
#17 

### Added
Added permissions

### Acceptance Criteria
Show a unauthorized access page when a user attempts to access product collections without the necessary permissions.

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Pranoy Sebastian <<EMAIL>>

Approvers
Approved-by: Aparna Gurumurthy <<EMAIL>>
Approved-by: Raghul R Nair <<EMAIL>>
































































































