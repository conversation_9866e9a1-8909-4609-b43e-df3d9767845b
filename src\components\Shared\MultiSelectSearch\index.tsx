import React, {useState} from 'react';
import {
    <PERSON><PERSON>ple<PERSON>,
    TextField,
    Box,
    ButtonBase,
    createTheme,
    ThemeProvider,
    styled,
    createFilterOptions,
} from '@mui/material/';
import {Add} from '@mui/icons-material';
import {allColors} from '@treez-inc/component-library';

const NoOptions = styled(Box)`
    align-items: center;
    color: #595959;
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-size: 0.938rem; /* 15px */
    line-height: 1.5rem; /* 24px */
    padding: 10px 0 15px 0;
`;

const NewDropdownOption = styled(Box)`
    color: #0f1709;
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-size: 0.938rem;
    line-height: 1.5rem;
    overflow: hidden;
    padding: 0 10px;
    text-overflow: ellipsis;
    white-space: nowrap;
`;

const CreateOptionContainer = styled(Box)`
    display: inline-flex;
    padding: 0 0 15px 0;
    width: 100%;
`;

const CreateOption = styled(ButtonBase)`
    align-items: center;
    background: #a9e079;
    border-radius: 99px;
    gap: 7.5px;
    height: 24px;
    flex-direction: row;
    justify-content: center;
    padding: 6px;
    width: 24px;
`;

const CreateOptionIcon = styled(Add)`
    box-sizing: border-box;
    height: 0.75em;
`;
export interface MenuItemsProps {
    displayValue: string | number;
    displayName: string;
}

export const convertToNumber = (inputString: string): Number | null => {
    let input = inputString.trim();
    if (input.match(/^\.[0-9]+/)) {
        input = `0${input}`; // e.g. '.5' -> '0.5'
    }
    const parsedValue = parseInt(input, 10);
    const isInvalid = Number.isNaN(parsedValue);
    return isInvalid ? null : parsedValue;
};

const filter = createFilterOptions<MenuItemsProps>();

interface MultiSelectWithSearchProps {
    label?: string;
    menuItems: MenuItemsProps[];
    onChange: any;
    onBlur?: any;
    value: MenuItemsProps[];
    isDisabled?: boolean;
    dataForNewOption?: any;
    mutation?: any;
    createPermissions?: string[];
    options?: {isValueANumber?: boolean; createNewOption?: boolean; parentOption?: string};
}

const MultiSelectSearch = React.forwardRef(
    (
        {
            menuItems: menuData,
            onChange,
            onBlur,
            value,
            isDisabled = false,
            options = {isValueANumber: false, createNewOption: false, parentOption: ''},
        }: MultiSelectWithSearchProps,
        ref
    ) => {
        const [isAutoCompleteOpen, setIsAutoCompleteOpen] = useState(false);
        const [menuItems, setMenuItems] = useState(menuData);
        const [newInputValue, setNewInputValue] = useState('');

        const autoCompleteTheme = createTheme({
            components: {
                MuiAutocomplete: {
                    styleOverrides: {
                        root: {
                            background: allColors.gray.main,
                            borderRadius: '19px',
                        },
                        inputRoot: {
                            borderRadius: '19px',
                            fontFamily: 'Roboto',
                            fontStyle: 'normal',
                            fontWeight: 400,
                            fontSize: '0.938rem',
                        },
                        popper: {
                            borderRadius: '4px 4px 17px 17px',
                            boxShadow: '0px 30px 40px rgba(0, 0, 0, 0.12)',
                        },
                        paper: {
                            background: allColors.grey02.main,
                            borderRadius: '4px 4px 17px 17px',
                            boxShadow: '0px 30px 40px rgba(0, 0, 0, 0.12)',
                        },
                        option: {
                            '&:hover': {
                                background: `${allColors.green03.main} !important`,
                            },
                            "&[aria-selected='true']": {
                                background: `${allColors.green03.main} !important`,
                            },
                            fontFamily: 'Roboto',
                            fontStyle: 'normal',
                            fontWeight: 400,
                            fontSize: '0.938rem',
                            lineHeight: '24px',
                        },
                    },
                },
            },
        });

        const getValue = (currentValue: string | any[]) => {
            const current =
                typeof currentValue === 'string' ? currentValue.split(',') : currentValue;
            return current;
        };

        const saveNewOption = async () => {
            const newOption = {name: newInputValue, id: newInputValue};

            const currentValue = {
                displayName: newOption?.name,
                displayValue: newOption?.id,
            };

            onChange([...value, currentValue]);

            setMenuItems((prevMenuItems: MenuItemsProps[]) => {
                const values = prevMenuItems || [];
                return [...values, currentValue];
            });
        };

        const handleChange = (__event: any, selectedOptions: MenuItemsProps[], reason: string) => {
            if (options.createNewOption || options?.parentOption !== 'Amount') {
                onChange(selectedOptions);
            } else {
                const lastTypedValue = newInputValue.trim();
                if (reason === 'select-option' && !lastTypedValue) {
                    return;
                }
                if (lastTypedValue) {
                    const lastSelectedOption = selectedOptions[selectedOptions.length - 1];
                    const newOption = {
                        displayName: `${lastTypedValue} ${lastSelectedOption.displayName}`,
                        displayValue: `${lastTypedValue} ${lastSelectedOption.displayValue}`,
                    };
                    onChange([...value, newOption]);
                    setNewInputValue('');
                } else {
                    onChange(selectedOptions);
                }
            }
        };

        const searchFilterOptions = (filterOptions: any, params: any) => {
            const inputValue: any = params.inputValue.trim();
            const isNumber =
                !Number.isNaN(parseFloat(inputValue)) && Number.isFinite(parseFloat(inputValue));
            if (
                options?.parentOption === 'Amount' &&
                (!inputValue || !isNumber || parseFloat(inputValue) === 0)
            ) {
                return [];
            }

            const isNumberOnly = /^\d+(\.\d+)?$/.test(inputValue);
            if (isNumberOnly) {
                return filterOptions;
            }
            const [numberPart, textPart] = inputValue.split(/\s+/);
            if (numberPart && textPart) {
                return filterOptions.filter((option: any) =>
                    option.displayName.toLowerCase().includes(textPart.toLowerCase())
                );
            }
            return filterOptions.filter((option: any) =>
                option.displayName.toLowerCase().includes(inputValue.toLowerCase())
            );
        };

        return (
            <>
                <ThemeProvider theme={autoCompleteTheme}>
                    <Autocomplete
                        id="multi-select-with-search"
                        open={isAutoCompleteOpen}
                        onOpen={() => setIsAutoCompleteOpen(true)}
                        onClose={() => setIsAutoCompleteOpen(false)}
                        autoHighlight
                        clearOnBlur
                        handleHomeEndKeys
                        multiple
                        disabled={isDisabled}
                        value={getValue(value)}
                        ref={ref}
                        options={menuItems || []}
                        selectOnFocus
                        sx={{width: '100%'}}
                        onBlur={onBlur}
                        onChange={handleChange}
                        onInputChange={(event, inputValue) => {
                            if (event?.type === 'change') {
                                setNewInputValue(inputValue.replace(/^\s+|\s+$|\s+(?=\s)/g, ''));
                            }
                        }}
                        filterOptions={
                            options.createNewOption
                                ? (filterOptions, params) => filter(filterOptions, params)
                                : searchFilterOptions
                        }
                        isOptionEqualToValue={(option, selectedOption) =>
                            option.displayValue === selectedOption.displayValue
                        }
                        getOptionLabel={(option: MenuItemsProps): string =>
                            option?.displayName || ''
                        }
                        noOptionsText={
                            <>
                                {options.createNewOption && !newInputValue && (
                                    <NoOptions className="multi-select-no-options">
                                        {options.isValueANumber
                                            ? 'Enter a Custom Number'
                                            : 'Enter a Custom Value'}
                                    </NoOptions>
                                )}
                                {options.createNewOption && newInputValue && (
                                    <CreateOptionContainer>
                                        <CreateOption
                                            onClick={() => {
                                                if (!newInputValue) {
                                                    return;
                                                }
                                                saveNewOption();
                                            }}
                                        >
                                            <CreateOptionIcon />
                                        </CreateOption>
                                        <NewDropdownOption>{newInputValue}</NewDropdownOption>
                                    </CreateOptionContainer>
                                )}
                                {!options.createNewOption &&
                                    !newInputValue &&
                                    options?.parentOption === 'Amount' && (
                                        <NoOptions className="multi-select-no-options">
                                            Enter a Custom Size and Select UOM
                                        </NoOptions>
                                    )}
                            </>
                        }
                        renderInput={(params) => (
                            <TextField
                                sx={{
                                    border: 'ActiveBorder',
                                }}
                                {...params}
                            />
                        )}
                        renderOption={(props, option) => <li {...props}>{option.displayName}</li>}
                    />
                </ThemeProvider>
            </>
        );
    }
);

export default MultiSelectSearch;
