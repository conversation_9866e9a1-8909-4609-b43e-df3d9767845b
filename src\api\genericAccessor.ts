import axios from 'axios';
import qs from 'qs';
import AuthTokens from '../interfaces/tokens';

// NOTE: This function will be re-set before it's used in the accessor functions below
let getAuthTokens = (): AuthTokens => {
    // eslint-disable-next-line no-console
    console.warn('Calling dummy function to retrieve auth tokens');
    return {
        accessToken: '',
        refreshToken: '',
        expiresIn: 0,
        idToken: '',
    };
};

export const setAuthTokensAccessor = (getTokensFunc: () => AuthTokens) => {
    getAuthTokens = getTokensFunc;
};

export const getAccessToken = (): string => getAuthTokens().accessToken;

const getAuthHeadersConfig = () => {
    const authTokens = getAuthTokens();
    if (!authTokens?.accessToken) {
        // eslint-disable-next-line no-console
        console.error('Missing the needed accessToken!');
    }

    return {
        headers: {
            Authorization: `Bearer ${authTokens?.accessToken}`,
        },
    };
};

export const getData = async (apiBaseUrl: string, entity: string, params: any) => {
    const result = await axios.get(`${apiBaseUrl}/${entity}`, {
        params,
        paramsSerializer: {
            serialize: (paramValues) => qs.stringify(paramValues, { arrayFormat: 'repeat' }),
        },
        ...getAuthHeadersConfig(),
    });
    return result.data;
};

export const createData = async (apiBaseUrl: string, entity: string, data: any) => {
    const result = await axios.post(
        `${apiBaseUrl}/${entity}`,
        data,
        getAuthHeadersConfig(),
    );
    return result.data;
};

export const updateData = async (apiBaseUrl: string, entity: string, data: any) => {
    const result = await axios.put(
        `${apiBaseUrl}/${entity}`,
        data,
        getAuthHeadersConfig(),
    );
    return result.data;
};

export const deleteData = async (apiBaseUrl: string, entity: string, data: any) => {
    const result = await axios.delete(`${apiBaseUrl}/${entity}`, {
        data,
        ...getAuthHeadersConfig(),
    });
    return result.data;
};
