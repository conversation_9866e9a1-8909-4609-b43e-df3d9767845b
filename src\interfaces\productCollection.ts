import { IVariantIds } from './variant';

export interface IRule {
  name?: String[];
  category?: String[] | [];
}
interface IProductCollection {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  sync: boolean;
  rule?: IRule;
}

export interface CollectionCreateDTO {
  id?: string;
  name: string;
  collectionItems: Array<IVariantIds> | [];
  sync: boolean;
  rule?: IRule;
}

export default IProductCollection;
