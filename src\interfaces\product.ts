import {IVariant} from './variant';

export interface IProduct {
    id: string;
    collectionId: string;
    collectionName: string;
    productId: string;
    productName: string;
    sku: string;
    brandId: string;
    brandName: string;
    productSubCategoryId: string;
    productSubCategoryName: string;
    productCategoryId: string;
    productCategoryName: string;
    allSizes: string;
    isChild?: boolean;
    hierarchy: string[];
    variants: IVariant[];
}
