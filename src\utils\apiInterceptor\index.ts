import axios, {AxiosInstance, AxiosError} from 'axios';
import retryRequest from '../retryRequest';
import IFrameworkProps from '../../interfaces/frameworkProps';

const apiInterceptor = (
    getTokens: IFrameworkProps['getTokens'],
    refreshTokens: IFrameworkProps['refreshTokens'],
    redirectToLogin: IFrameworkProps['redirectToLogin']
) => {
    const api: AxiosInstance = axios.create();

    api.interceptors.response.use(undefined, async (err: AxiosError) => {
        const {config, message} = err;

        const response = err.response?.status;

        // Retry only in cases of Network timeout, Network Error or 401 Unauthorized response
        if (
            !(message.includes('timeout') || message.includes('Network Error') || response === 401)
        ) {
            return Promise.reject(err);
        }

        // Attempt to refresh tokens on 401 Unauthorized responses
        if (response === 401) {
            await refreshTokens();
        }

        if (config?.headers?.retry !== undefined) {
            const retryHeader = config.headers.retry as string;

            let retryHeaderInt = parseInt(retryHeader, 10);

            retryHeaderInt += 1;

            if (retryHeaderInt > 3) {
                return response === 401 ? redirectToLogin() : Promise.reject(err);
            }

            config.headers.retry = retryHeaderInt.toString();
            config.headers.Authorization = getTokens().accessToken;
            return retryRequest(config, api, config.headers);
        }

        return Promise.reject(err);
    });

    return api;
};

export default apiInterceptor;
