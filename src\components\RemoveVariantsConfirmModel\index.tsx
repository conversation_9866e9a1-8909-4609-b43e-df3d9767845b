import React, { Dispatch, SetStateAction } from 'react';
import { Modal } from "@treez-inc/component-library";
import {
    GridRowSelectionModel,
} from '@mui/x-data-grid-pro';
import { IProduct } from "../../interfaces/product";
import { RemoveVariantsConfirmModalContent } from "../RemoveVariantsConfirmModalContent";
import { calculateSelectedVariantsLength } from '../../utils/filterUtil';

interface RemoveVariantsConfirmModalProps {
    allProducts: IProduct[];
    removeVariants: () => Promise<void>;
    toggleRemoveVariantsConfirmModal: Dispatch<SetStateAction<boolean>>;
    removeVariantsConfirmModalOpen: boolean;
    collectionName: string,
    selectionModel: GridRowSelectionModel,
    selectedProductsCount: number,
    isRemoveButtonDisabled: boolean,
}

export const RemoveVariantsConfirmModal: React.FC<RemoveVariantsConfirmModalProps> = ({
    allProducts,
    removeVariants,
    toggleRemoveVariantsConfirmModal,
    removeVariantsConfirmModalOpen,
    collectionName,
    selectionModel,
    selectedProductsCount,
    isRemoveButtonDisabled,
}) => {
    const closeConfirmation = () => toggleRemoveVariantsConfirmModal(false);
    const totalSelectedVariants = calculateSelectedVariantsLength(selectionModel, allProducts);

    return <Modal
        content={
            <RemoveVariantsConfirmModalContent
                collectionName={collectionName}
                selectionModel={selectionModel}
                modifiedData={allProducts}
                selectedProductsCount={selectedProductsCount}
            />
        }
        onClose={closeConfirmation}
        open={removeVariantsConfirmModalOpen}
        primaryButton={{
            label:
                totalSelectedVariants === allProducts.length ? 'Empty collection' : 'Remove',
            onClick: async () => {
                closeConfirmation();
                removeVariants();
            },
            disabled: isRemoveButtonDisabled,
            testId: 'confirm-remove-variant-button'
        }}
        secondaryButton={{
            label: 'Cancel',
            onClick: closeConfirmation,
            disabled: isRemoveButtonDisabled,
            testId: 'cancel-remove-variant-button'
        }}
        testId="confirmation-modal"
        title={
            totalSelectedVariants === allProducts.length
                ? 'Empty collection'
                : 'Removing products from collection'
        }
    />
};