import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { TreezThemeProvider } from '@treez-inc/component-library';
import ApiService from '../../utils/apiService';
import SnackbarProvider from '../../providers/SnackbarProvider';
import AddManualProductCollection from '.';

const mockedUseNavigate = jest.fn();
jest.mock('../../hooks/usePermissionsContext');
jest.mock('../../hooks/useApiContext');
jest.mock('../../hooks/useSnackbarContext');
jest.mock('react-router-dom', () => ({
  useNavigate: () => mockedUseNavigate,
}));

const mockUseApiContext = require('../../hooks/useApiContext').default;
const mockUseSnackbarContext = require('../../hooks/useSnackbarContext').default;

const expectedSuccessSnackbarMsg = {
  message: 'Collection dummyCollection is created.',
  severity: 'info',
  iconName: 'Success',
};

const expectedFailureSnackbarMsg = {
  message: 'We can not create dummyCollection collection. Please try again later.',
  severity: 'error',
  iconName: 'Error',
};

const validTokens = {
  accessToken: 'accessToken',
  expiresIn: 300,
  refreshToken: 'refreshToken',
  idToken: 'idToken',
};
const clearTokens = jest.fn();
const getTokens = () => validTokens;
const redirectToLogin = jest.fn();
const apiService = new ApiService(getTokens, clearTokens, redirectToLogin);
const spyApiPost = jest.spyOn(apiService.api, 'post');

const closeModal = jest.fn();
const appendCollection = jest.fn();
const mockSetSnackbar = jest.fn();
const setIsLoading = jest.fn();

const productCollectionData = [
  {
    id: 'd393a44a-ecfe-4084-9554-a20bc59d7d59',
    name: 'dummyCollectionA',
    createdAt: '2023-11-20T08:00:50.864Z',
    updatedAt: '2023-11-20T08:00:50.864Z',
    deletedAt: null,
    sync: false
  },
  {
    id: 'd393a44a-ecfe-4084-9554-a20bc59d7d58',
    name: 'dummyCollectionB',
    createdAt: '2023-11-20T08:00:50.864Z',
    updatedAt: '2023-11-20T08:00:50.864Z',
    deletedAt: null,
    sync: false

  },
  {
    id: 'd393a44a-ecfe-4084-9554-a20bc59d7d58',
    name: 'dummyCollectionC',
    createdAt: '2023-11-20T08:00:50.864Z',
    updatedAt: '2023-11-20T08:00:50.864Z',
    deletedAt: '2023-11-20T08:00:50.864Z',
    sync: false,
  },
  {
    id: 'd393a44a-ecfe-4084-9554-a20bc59d7d58',
    name: 'TesingMaxLengthNameForCollectionNameInTheModalAddCollectionProductCollection',
    createdAt: '2023-11-20T08:00:50.864Z',
    updatedAt: '2023-11-20T08:00:50.864Z',
    deletedAt: null,
    sync: false
  }
];

const apiResponse = {
  data: {
    id: 'd393a44a-ecfe-4084-9554-a20bc59d7d59',
    name: 'dummyCollection',
    organizationId: '8ca822f9-4915-41db-a9ba-9d7eb63cad33',
    sync: false,
    createdAt: '2023-11-20T08:00:50.864Z',
    updatedAt: '2023-11-20T08:00:50.864Z',
    deletedAt: ''
  },
};

describe('<AddManualProductCollection/>', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    mockUseApiContext.mockImplementation(() => apiService);
    spyApiPost.mockResolvedValue(apiResponse);
    mockUseSnackbarContext.mockReturnValue({
      setSnackbar: mockSetSnackbar,
    });
  });

  const renderAddCollectionModalComponent = () => {
    const { getByTestId } = render(
      <TreezThemeProvider>
        <SnackbarProvider>
          <AddManualProductCollection
            closeModal={closeModal}
            productCollectionData={productCollectionData}
            appendCollection={appendCollection}
            setIsLoading={setIsLoading}
          />
        </SnackbarProvider>
      </TreezThemeProvider>
    );

    const nameField = getByTestId('collection-name-field');
    const nameInput = nameField.querySelector('input');
    const getNameHelperText = () => nameField.querySelector('p');
    const primaryButton = getByTestId('primaryButton-add-collection-modal');
    return {
      nameField,
      nameInput,
      getNameHelperText,
      primaryButton,
    };
  };

  it('should render component', () => {
    renderAddCollectionModalComponent();

    expect(screen.getByTestId('add-product-collection-form')).toBeInTheDocument();
  });

  it('should not show error message on entering unique collection name', async () => {
    const { nameInput, getNameHelperText, primaryButton } = renderAddCollectionModalComponent();

    if (nameInput) {
      fireEvent.change(nameInput, { target: { value: 'uniqueName123' } });
    }

    await waitFor(() => {
      expect(getNameHelperText()).toBeNull();
      expect(primaryButton).not.toHaveAttribute('disabled');
    });
  });

  it('should show error message on entering existing collection name', async () => {
    const { nameInput, getNameHelperText, primaryButton } = renderAddCollectionModalComponent();

    if (nameInput) {
      fireEvent.change(nameInput, { target: { value: 'dummyCollectionA' } });
    }

    await waitFor(() => {
      expect(getNameHelperText()).toBeInTheDocument();
      expect(primaryButton).toHaveAttribute('disabled');
    });
  });

  it('should call post api when Add Collection button is clicked', async () => {
    const { nameInput, primaryButton } = renderAddCollectionModalComponent();

    if (nameInput) {
      fireEvent.change(nameInput, { target: { value: 'dummyCollection' } });
    }

    fireEvent.click(primaryButton);

    await waitFor(() => {
      expect(spyApiPost).toBeCalled();
      expect(closeModal).toBeCalled();
      expect(appendCollection).toBeCalled();
      expect(appendCollection).toBeCalledWith(apiResponse.data);
      expect(mockSetSnackbar).toHaveBeenCalledTimes(1);
      expect(mockSetSnackbar).toHaveBeenCalledWith(expectedSuccessSnackbarMsg);
    });
  });

  it('should log error on api post failure', async () => {
    spyApiPost.mockRejectedValue(new Error('failure'));

    const { nameInput, primaryButton } = renderAddCollectionModalComponent();

    if (nameInput) {
      fireEvent.change(nameInput, { target: { value: 'dummyCollection' } });
    }

    fireEvent.click(primaryButton);

    await waitFor(() => {
      expect(spyApiPost).toBeCalled();
      expect(appendCollection).not.toBeCalled();
      expect(mockSetSnackbar).toHaveBeenCalledTimes(1);
      expect(mockSetSnackbar).toHaveBeenCalledWith(expectedFailureSnackbarMsg);
    });
  });

  it('should show alert message on entering existing collection name and user should be able to dismiss the Alert ', async () => {
    const { nameInput, getNameHelperText, primaryButton } = renderAddCollectionModalComponent();

    if (nameInput) {
      fireEvent.change(nameInput, { target: { value: 'dummyCollectionA' } });
    }

    await waitFor(() => {
      expect(getNameHelperText()).toBeInTheDocument();
      expect(primaryButton).toHaveAttribute('disabled');
      const alert = screen.getByTestId('duplicate-collection-name-alert');
      expect(alert).toBeInTheDocument();
      const dismissButton = screen.getByRole("button", { name: "Dismiss" });
      expect(dismissButton).toBeInTheDocument();
      if (dismissButton) {
        fireEvent.click(dismissButton);
      }
    });

  });

  it('should show error message on entering maximum length existing collection name', async () => {
    const { nameInput, getNameHelperText, primaryButton } = renderAddCollectionModalComponent();

    if (nameInput) {
      fireEvent.change(nameInput, { target: { value: 'TesingMaxLengthNameForCollectionNameInTheModalAddCollectionProductCollection' } });
    }

    await waitFor(() => {
      expect(getNameHelperText()).toBeInTheDocument();
      expect(primaryButton).toHaveAttribute('disabled');
      expect.any(Error);
    });

  });

  it('should show error message on entering maximum length collection name', async () => {
    const { nameInput, getNameHelperText, primaryButton } = renderAddCollectionModalComponent();

    if (nameInput) {
      fireEvent.change(nameInput, { target: { value: 'MaxLengthNameForCollectionNameInTheModalAddCollectionProductCollection' } });
    }

    await waitFor(() => {
      expect(getNameHelperText()).toBeInTheDocument();
      expect(primaryButton).toHaveAttribute('disabled');
      expect.any(Error);
    });

  });
});
