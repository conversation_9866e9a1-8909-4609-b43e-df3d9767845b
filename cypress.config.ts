import {defineConfig} from 'cypress';
import {GetParameterCommand} from '@aws-sdk/client-ssm';
import {getCypressEnv} from './cypress/support/cypressEnv';
import {logger} from './cypress/support/logging';
import ssmClient from './cypress/support/ssmClient';

export default defineConfig({
    video: false,
    defaultCommandTimeout: 10000,
    e2e: {
        setupNodeEvents(on, config) {
            logger.debug(JSON.stringify(config, null, 2));

            const passwordCache: Record<string, string> = {};

            on('task', {
                getPassword(username) {
                    if (passwordCache[username] !== undefined) {
                        logger.debug(`Returning password from cache for ${username}`);
                        return {error: null, password: passwordCache[username]};
                    }
                    const command = new GetParameterCommand({
                        Name: `/qa/user/${username.replace('+', '_').replace('@treez.io', '')}`,
                        WithDecryption: true,
                    });

                    return ssmClient.send(command).then(
                        (data) => {
                            const pw = data.Parameter?.Value ?? null;
                            if (pw !== null) {
                                passwordCache[username] = pw;
                            }
                            logger.debug(`Returning password from SSM for ${username}`);
                            return {error: null, password: pw};
                        },
                        (error) => {
                            logger.error(`Error getting password from SSM: ${error}`);
                            return {error, password: null};
                        }
                    );
                },
            });
        },
        env: {
            sandbox: getCypressEnv('sandbox'),
            build: getCypressEnv('build'),
        },
    },
});
