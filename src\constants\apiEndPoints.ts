const BASE_URL = process.env.DOMAIN_URL;
const ORGANIZATION_BASE_URL = process.env.ORGANIZATION_API_BASE_URL;

export const COLLECTIONS_URL = `${BASE_URL}/product-collection/v1`;
export const PRODUCT_CONTROL_URL = `${process.env.SERVICE_URL}/product-control`;
export const PRODUCT_CONTROL_API_URL = `${BASE_URL}/product-management`;

export const orgFeatureFlagsUrl = ({
  orgId,
  featureFlag,
}: {
  orgId: string;
  featureFlag: string;
}) =>
  `${BASE_URL}/${ORGANIZATION_BASE_URL}/organizations/${orgId}/feature-flag?service=${featureFlag}`;