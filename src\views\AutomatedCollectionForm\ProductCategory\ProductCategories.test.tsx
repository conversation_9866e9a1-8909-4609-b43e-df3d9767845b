import React from 'react';
import '@testing-library/jest-dom';
import { fireEvent, render } from '@testing-library/react';
import { TreezThemeProvider } from '@treez-inc/component-library';
import ProductCategories from ".";

const productCatergoryList = [{
    "id": "83d31f80-65d1-4aa8-bb63-37cf9ec81b0a",
    "createdAt": "2023-08-11T05:51:00.161Z",
    "updatedAt": "2023-08-11T05:51:00.161Z",
    "deletedAt": null,
    "name": "Test Category",
    "isCannabis": true,
    "uoms": [
        {
            "label": "GRAMS",
            "value": "GRAMS"
        }
    ],
    "productFormDetails": [],
    "variantFormDetails": []
}];

const collectionProductCategory = jest.fn();
const onCategoryClick = jest.fn();

describe('<ProductCategories/>', () => {
    it('ProductCategories renders properly', () => {
        const { getByTestId } = render(
            <TreezThemeProvider>
                <ProductCategories
                    parentCategoriesList={productCatergoryList}
                    collectionProductCategory={collectionProductCategory}
                    onCategoryClick={onCategoryClick}
                />
            </TreezThemeProvider>
        );
        const categoryContainer = getByTestId('product-categories-container');
        const testCategory = getByTestId('category-button-Test Category')
        expect(categoryContainer).toBeInTheDocument();
        fireEvent.click(testCategory);
        expect(testCategory).toBeInTheDocument();
    });
});
