import {styled} from '@mui/material/styles';
import React from 'react';

const PageContainer = styled('div')(({theme}) => ({
    width: '100%',
    height: '100%',
    overflowY: 'auto',
    background: theme.palette.primaryWhite.main,
}));

export interface PageLayoutProps {
    children: React.ReactNode;
    testId: string;
}

const PageLayout: React.FC<PageLayoutProps> = ({children, testId}: PageLayoutProps) => (
    <PageContainer data-testid={testId}>{children}</PageContainer>
);

export default PageLayout;
