import {act, renderHook} from '@testing-library/react';
import useSnackbar from '.';

const snack = {
    message: 'Great job!',
    severity: 'info',
    iconName: 'Success',
} as const;

describe('Use Snackbar', () => {
    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });
    it('can open snackbar with snack', async () => {
        const {result} = await renderHook(() => useSnackbar());
        const handleSnack = result.current[0];
        await act(async () => {
            handleSnack(snack);
        });

        const state = result.current[1];
        expect(state.message).toEqual(snack.message);
        expect(state.iconName).toEqual(snack.iconName);
        expect(state.severity).toEqual(snack.severity);
        expect(state.open).toBe(true);
        expect(state.onClose).toEqual(expect.any(Function));
    });

    it('can close snackbar', async () => {
        const {result} = await renderHook(() => useSnackbar());
        const handleSnack = result.current[0];
        const handleClose = result.current[1].onClose;
        await act(async () => {
            handleSnack(snack);
            handleClose();
        });

        const state = result.current[1];
        expect(state.message).toEqual('');
        expect(state.iconName).toBeUndefined();
        expect(state.severity).toBeUndefined();
        expect(state.open).toBe(false);
        expect(state.onClose).toEqual(expect.any(Function));
    });
});
