import {GridRowId, GridRowSelectionModel} from '@mui/x-data-grid-pro';
import {IFilterState} from '../../interfaces/collectionFilter';
import CollectionStatus from '../../interfaces/collectionStatus.enum';
import {IProduct} from '../../interfaces/product';
import IProductCollection from '../../interfaces/productCollection';
import {IVariantIds} from '../../interfaces/variant';

const filterCollectionList = (collectionList: IProductCollection[], filterObj: IFilterState) => {
    let filteredList;

    if (filterObj.status?.length === 0) {
        filteredList = collectionList;
    } else {
        filteredList = collectionList.filter((collection) => {
            const matchesStatus =
                (filterObj.status.includes(CollectionStatus.ACTIVE) && !collection.deletedAt) ||
                (filterObj.status.includes(CollectionStatus.INACTIVE) && collection.deletedAt);

            if (filterObj.status.length) {
                return matchesStatus;
            }

            return matchesStatus;
        });
    }

    return filteredList;
};

const getAllProductsData = (products: IProduct[] | undefined = []) =>
    products?.filter((product) => product.isChild === false);

const getSelectedProductsData = (
    selectedIds: GridRowSelectionModel,
    products: IProduct[] | undefined = []
) =>
    products
        ?.filter((product) => selectedIds.includes(product.id as GridRowId))
        .map((product) => ({id: product.id}));

const selectedProductVariants = (selectedIds: any, productList: any) => {
    const selectedChildProducts = selectedIds.filter((selectedId: any) =>
        productList.some((product: any) => product.id === selectedId && product.isChild === true)
    );

    return selectedChildProducts;
};

const removeVariantsAndExtractIds = (
    products: IProduct[],
    variantIdsToRemove: string[]
): IVariantIds[] => {
    const updatedProducts = products.map((product) => ({
        ...product,
        variants: product.variants
            ? product.variants.filter((variant) => !variantIdsToRemove.includes(variant.id))
            : [],
    }));

    const removedVariantIds: IVariantIds[] = updatedProducts.flatMap((product) =>
        product.variants.map((variant) => ({variantId: variant.id}))
    );

    return removedVariantIds;
};

const getProductByVariantIds = (
    products: IProduct[],
    selectedVariantIds: GridRowId[]
): String[] => {
    const selectedProducts: string[] = [];

    products.forEach((product) => {
        if (product.variants) {
            product.variants.forEach((variant) => {
                if (
                    !selectedProducts.includes(product.productId) &&
                    selectedVariantIds.includes(variant.id) &&
                    !product.isChild
                ) {
                    selectedProducts.push(product.productId);
                }
            });
        }
    });

    return selectedProducts;
};

const areAllVariantsSelected = (
    products: IProduct[],
    parentId: string,
    selectedIds: GridRowSelectionModel
): boolean => {
    const childProducts = products.filter(
        (product) => product.isChild && product.productId === parentId
    );
    const childIds = childProducts.map((child) => child.id);

    return childIds.every((childId) => selectedIds.includes(childId));
};

const removeSelectedProducts = (products: IProduct[], selectedIds: any): IProduct[] => {
    const updatedProducts = products
        .map((product) => {
            const isSelected = selectedIds.includes(product.id);
            const selectedParentId = product.productId;
            const allVariantsSelected = areAllVariantsSelected(
                products,
                selectedParentId,
                selectedIds
            );
            const shouldRemoveProduct =
                (product.isChild && isSelected) ||
                (!product.isChild && allVariantsSelected && product.variants);

            return shouldRemoveProduct ? null : {...product};
        })
        .filter(Boolean) as IProduct[];

    updatedProducts.forEach((product) => {
        if (
            !product.isChild &&
            product.variants &&
            product.variants.some((variant) => selectedIds.includes(variant.id))
        ) {
            const updatedVariants = product.variants.filter(
                (variant) => !selectedIds.includes(variant.id)
            );
            // eslint-disable-next-line no-param-reassign
            product.variants = updatedVariants;
        }
    });

    return updatedProducts;
};

const hasVariantSelected = (selectedIds: any, productList: any) =>
    selectedIds.some((selectedId: any) =>
        productList.some((product: any) => product.id === selectedId && product.isChild === true)
    );


const calculateSelectedVariantsLength = (
    selectedIds: GridRowSelectionModel,
    products: IProduct[] = []
) => (products ? getSelectedProductsData(selectedIds, products).length : 0);



const findUniqueIds = (arr1: any[], arr2: any[]) => {
    const set1 = new Set(arr1);
    const set2 = new Set(arr2);
  
    const uniqueIds = [
      ...arr1.filter(id => !set2.has(id)),
      ...arr2.filter(id => !set1.has(id))
    ];
  
    return uniqueIds;
};

const removeIds = (mainArray: any[], idsToRemove: any[]) => {
    const removeSet = new Set(idsToRemove);
    return mainArray.filter(id => !removeSet.has(id));
};

const removeEmptyArrays = (obj: any): any => 
    Object.entries(obj)
        .filter(([, value]) => !(Array.isArray(value) && value.length === 0))
        .reduce((acc, [key, value]) => {
            acc[key] = value;
            return acc;
        }, {} as any);

const addUniqueIds = (targetArray: (string | GridRowId)[], sourceArray: (string | GridRowId)[]) => {
    sourceArray.forEach(id => {
        if (!targetArray.includes(id)) {
            targetArray.push(id);
        }
    });
};

const removeMatchingIds = (targetArray: (string | number)[], sourceArray: (string | number)[]) => {
    // eslint-disable-next-line no-plusplus
    for (let i = targetArray.length - 1; i >= 0; i--) {
        if (sourceArray.includes(targetArray[i])) {
            targetArray.splice(i, 1);
        }
    }
};

export {
    filterCollectionList,
    removeVariantsAndExtractIds,
    selectedProductVariants,
    getAllProductsData,
    getSelectedProductsData,
    areAllVariantsSelected,
    removeSelectedProducts,
    hasVariantSelected,
    getProductByVariantIds,
    calculateSelectedVariantsLength,
    findUniqueIds,
    removeIds,
    removeEmptyArrays,
    addUniqueIds,
    removeMatchingIds,
};
