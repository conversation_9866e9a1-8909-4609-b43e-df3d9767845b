import CollectionStatus from './collectionStatus.enum';

export interface IFilterState {
    status: string[];
}

export interface IFilterComponent {
    onChange: any;
}

export interface IFilterCheckbox {
    key: string;
    label: string;
    checked: boolean;
    onChange: (checkboxKey: string | number, isChecked: boolean) => void;
    value: string;
}

export interface CollectionStatusCheckbox {
    key: CollectionStatus;
    value: string;
    label: string;
    checked?: boolean;
}

export interface IFilterDropdown {
    filterItem: string;
    label: string;
    chipId: string;
    menuId: string;
    values: any;
    onChange: (a: string, b: IFilterCheckbox[]) => void;
    badgeContent?: number | string;
    status?: string[];
}
