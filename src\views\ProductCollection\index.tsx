import React, {useState} from 'react';
import {Breadcrumbs, convertPxToRem, Button} from '@treez-inc/component-library';
import {Box, styled} from '@mui/material';
import {useLocation} from 'react-router-dom';
import PageLayout from '../../components/PageLayout';
import useProductCollections from '../../hooks/useProductCollections';
import GridLayout from '../../components/GridLayout';
import ProductCollectionGrid from '../../components/ProductCollectionGrid';
import RouteError from '../../components/RouteError';
import AddProductCollection from '../AddProductCollection';
import usePermissionsContext from '../../hooks/usePermissionsContext';
import {MFEPermissions} from '../../interfaces/permissions';
import AddManualProductCollection from '../AddManualProductCollection';
import LoadingSpinner from '../../components/Shared/LoadingSpinner';
import useGetFeatureFlag from '../../hooks/useGetFeatureFlag';
import {orgFeatureFlagsUrl} from '../../constants/apiEndPoints';

const StyledHeader = styled('div')(({theme}) => ({
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    background: theme.palette.grey02.main,
}));

const StyleBreadcrumbs = styled(Box)(({theme}) => ({
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    alignContent: 'center',
    justifyContent: 'center',
    background: theme.palette.grey02.main,
    paddingTop: convertPxToRem(24),
    paddingBottom: convertPxToRem(24),
    paddingLeft: convertPxToRem(52),
    paddingRight: convertPxToRem(52),
}));

const StyleAddButton = styled(Box)(() => ({
    width: '33%',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingLeft: convertPxToRem(52),
    paddingRight: convertPxToRem(52),
}));

const ProductCollection: React.FC = () => {
    const location = useLocation();
    const {state} = location;
    const createdCollection = state ? state.collection : null;
    const {permissions, orgId} = usePermissionsContext();
    const hasCollectionManagePermission =
        permissions![MFEPermissions.MANAGE_PRODUCT_COLLECTIONS] === true;

    const automatedDiscountsFeatureFlag = useGetFeatureFlag(
        orgFeatureFlagsUrl({
            orgId: orgId || '',
            featureFlag: 'Automated Discounts',
        })
    );

    const [isAddProductCollectionModal, setAddProductCollectionModal] = useState(false);
    const [isManulAddProductCollectionModal, setManualAddProductCollectionModal] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    const closeAddCollectionModal = () => {
        setAddProductCollectionModal(false);
    };

    const closeManualAddCollectionModal = () => {
        setManualAddProductCollectionModal(false);
    };

    const {data, loading, error, appendCollection, deleteCollection} = useProductCollections();

    if (error !== null) {
        return <RouteError error={error} />;
    }

    const rowModels =
        data &&
        data?.data?.map((element) => ({
            ...element,
            deleteCollection,
        }));

    if (!loading && data && createdCollection) {
        appendCollection(createdCollection);
    }

    return (
        <PageLayout testId="collections-page-layout">
            <StyledHeader data-testid="collections-header">
                <StyleBreadcrumbs>
                    <Breadcrumbs
                        ariaLabel="Collections Breadcrumbs"
                        links={[
                            {
                                ariaLabel: 'Catalog',
                                children: 'Catalog',
                                href: `/product-control`,
                            },
                            {
                                ariaLabel: 'Collections',
                                children: 'Collections',
                                href: `/product-collection`,
                            },
                        ]}
                        testId="collection-breadcrumbs"
                    />
                </StyleBreadcrumbs>
                {hasCollectionManagePermission && (
                    <StyleAddButton>
                        <Button
                            testId="add-product-collection-button"
                            label="Add Collection"
                            onClick={() => {
                                setAddProductCollectionModal(true);
                            }}
                        />
                    </StyleAddButton>
                )}
            </StyledHeader>
            {isLoading && <LoadingSpinner />}
            <GridLayout testId="collections-grid-layout">
                {rowModels && (
                    <ProductCollectionGrid
                        data={rowModels || []}
                        loading={loading}
                        setIsLoading={setIsLoading}
                    />
                )}
                {hasCollectionManagePermission && isAddProductCollectionModal && data && (
                    <AddProductCollection
                        closeModal={closeAddCollectionModal}
                        productCollectionData={data.data}
                        setIsLoading={setIsLoading}
                    />
                )}
                {hasCollectionManagePermission && isManulAddProductCollectionModal && data && (
                    <AddManualProductCollection
                        closeModal={closeManualAddCollectionModal}
                        productCollectionData={data.data}
                        appendCollection={appendCollection}
                        setIsLoading={setIsLoading}
                    />
                )}
            </GridLayout>
        </PageLayout>
    );
};

export default ProductCollection;
