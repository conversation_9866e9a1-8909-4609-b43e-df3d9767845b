import React from 'react';
import '@testing-library/jest-dom';
import {fireEvent, render} from '@testing-library/react';
import {TreezThemeProvider} from '@treez-inc/component-library';
import SnackbarProvider from '../../../providers/SnackbarProvider';
import CollectionFormButtons from '.';

describe('<CollectionFormButtons/>', () => {
    it('CollectionFormButtons renders properly and buttons trigger correct actions', () => {
        const handleAutomatedCollectionSubmitMock = jest.fn();
        const closeAutomatedCollectionFormMock = jest.fn();
        const submitTypeMock = 'Create';
        const isDeletedCollectionMock = false;
        const isUpdatePendingMock = true;

        const {getByTestId} = render(
            <TreezThemeProvider>
                <SnackbarProvider>
                    <CollectionFormButtons
                        handleAutomatedCollectionSubmit={handleAutomatedCollectionSubmitMock}
                        isAddAutomatedCollectionDisabled={false}
                        closeAutomatedCollectionForm={closeAutomatedCollectionFormMock}
                        submitType={submitTypeMock}
                        isDeletedCollection={isDeletedCollectionMock}
                        isUpdatePending={isUpdatePendingMock}
                        change
                    />
                </SnackbarProvider>
            </TreezThemeProvider>
        );

        const cancelButton = getByTestId('cancel-collection-form-button');
        const createButton = getByTestId('create-collection-form-button');

        fireEvent.click(cancelButton);
        fireEvent.click(createButton);

        expect(closeAutomatedCollectionFormMock).toHaveBeenCalledTimes(1);
        expect(handleAutomatedCollectionSubmitMock).toHaveBeenCalledTimes(1);
    });
});
