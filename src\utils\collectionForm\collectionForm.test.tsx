import collectionForm from '.';
import { IProductCollectionData } from '../../interfaces/productCollectionResponse';

describe('checkCollectionForm', () => {
    let collectionA: IProductCollectionData;

    let collectionB: IProductCollectionData;

    let collectionC: IProductCollectionData;

    let collections: IProductCollectionData[];

    beforeEach(() => {
        collectionA = {
            id: 'b138b43b-a4bd-4682-b133-2e4f8ad48fa1',
            name: 'dummyCollectionA',
            createdAt: '2023-11-21T12:15:21.222Z',
            updatedAt: '2023-11-21T12:15:21.222Z',
            deletedAt: null,
            sync: false,
        };
        collectionB = {
            id: 'b138b43b-a4bd-4682-b133-2e4f8ad48fa2',
            name: 'dummyCollectionB',
            createdAt: '2023-11-21T12:15:21.222Z',
            updatedAt: '2023-11-21T12:15:21.222Z',
            deletedAt: null,
            sync: false,
        };
        collectionC = {
            id: 'b138b43b-a4bd-4682-b133-2e4f8ad48fa2',
            name: 'dummyCollectionB',
            createdAt: '2023-11-21T12:15:21.222Z',
            updatedAt: '2023-11-21T12:15:21.222Z',
            deletedAt: '2023-11-21T12:15:21.222Z',
            sync: false,
        };
        collections = [collectionA, collectionB, collectionC];
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    const mockExistingCollectionName = 'existingDummyCollection';

    it('should return false if collections are empty', () => {
        collections = [];
        const collectionName = 'testCollection';
        const isExist = collectionForm.checkActiveCollectionNameExist(collections, collectionName, mockExistingCollectionName);

        expect(isExist).not.toBeTruthy();
    });
    it('should return true if collection name exist', () => {
        const collectionName = 'dummyCollectionA';

        const isExist = collectionForm.checkActiveCollectionNameExist(collections, collectionName, mockExistingCollectionName);

        expect(isExist).toBeTruthy();
    });

    it('should return false if collection name does not exist', () => {
        const collectionName = 'dummyCollectionD';

        const isExist = collectionForm.checkActiveCollectionNameExist(collections, collectionName, mockExistingCollectionName);

        expect(isExist).not.toBeTruthy();
    });

    it('should return false if the collection is deleted', () => {
        const collectionName = 'dummyCollectionC';

        const isExist = collectionForm.checkActiveCollectionNameExist(collections, collectionName, mockExistingCollectionName);

        expect(isExist).toBeFalsy();
    });

    it('should return false if collection name character length should not exceeds 64 chars', () => {
        const collectionName = '1234567890123456789012345678901234567890123456789012345678901234';

        const isExist = collectionForm.checkCollectionNameLength(collectionName);

        expect(isExist).not.toBeTruthy();
    });

    it('should return true if collection name character length exceeds 64 chars', () => {
        const collectionName = '12345678901234567890123456789012345678901234567890123456789012345';

        const isExist = collectionForm.checkCollectionNameLength(collectionName);

        expect(isExist).toBeTruthy();
    });
});
