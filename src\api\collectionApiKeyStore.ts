import { MutationKeyConfig, QueryKeyConfig } from './types';

const collectionApiKeyStore = {
    getProductCollectionDetails: (collectionId?: string): QueryKeyConfig => ({
        queryKey: ['GET_PRODUCT_COLLECTION_DETAILS', collectionId],
        route: `${collectionId}`,
        isEnabled: !!collectionId,
    }),
    upsertProductCollection: (collectionId?: string): MutationKeyConfig => ({
        mutationKey: ['UPSERT_PRODUCT_COLLECTION'],
        route: `${collectionId}`,
    }),
};

export default collectionApiKeyStore;