import React from 'react';
import {Box, styled} from '@mui/material/';

const StyledContentContainer = styled(Box)(() => ({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
}));

export interface CenteredContentProps {
    children: JSX.Element;
    testId?: string;
}

const CenteredContent: React.FC<CenteredContentProps> = ({
    children,
    testId = 'centered-content',
}) => <StyledContentContainer data-testid={testId}>{children}</StyledContentContainer>;

export default CenteredContent;
