import { createQueryKeyStore } from '@lukemorales/query-key-factory';
import { createData, getData } from '../api/genericAccessor';
import Entities from '../interfaces/entities.enum';
import type { ProductSearchQueryProps } from './useProductSearchQuery';
import { PRODUCT_CONTROL_API_URL } from '../constants/apiEndPoints';

const queryKeyStore = createQueryKeyStore({
    brand: {
        all: null,
        list: (listQueryParams: { ids?: string[] }) => ({
            queryKey: [{ ...listQueryParams }],
            queryFn: () => getData(PRODUCT_CONTROL_API_URL, Entities.BRAND, listQueryParams),
        }),
    },
    product: {
        all: null,
        list: (listQueryParams: { ids?: string[] }) => ({
            queryKey: [{ ...listQueryParams }],
            queryFn: () => getData(PRODUCT_CONTROL_API_URL, Entities.PRODUCT, listQueryParams),
        }),
    },
    variant: {
        all: null,
        list: (listQueryParams: { ids?: string[]; productId?: string }) => ({
            queryKey: [{ ...listQueryParams }],
            queryFn: () => getData(PRODUCT_CONTROL_API_URL, Entities.VARIANT, listQueryParams),
        }),
    },
    productCategory: {
        all: null,
        list: (listQueryParams?: any) => ({
            queryKey: [{ ...listQueryParams }],
            queryFn: () => getData(PRODUCT_CONTROL_API_URL, Entities.PRODUCT_CATEGORY, listQueryParams),
        }),
    },
    price: {
        all: null,
        list: (listQueryParams: { variantIds?: string[] }) => ({
            queryKey: [{ ...listQueryParams }],
            queryFn: () => getData(PRODUCT_CONTROL_API_URL, Entities.PRICE, listQueryParams),
        }),
    },
    store: {
        all: null,
        list: (listQueryParams?: any) => ({
            queryKey: [{ ...listQueryParams }],
            queryFn: () => getData(PRODUCT_CONTROL_API_URL, Entities.ORGANIZATION_ENTITY, {}),
        }),
    },
    attributeCategory: {
        all: null,
        list: (listQueryParams: {}) => ({
            queryKey: [{ ...listQueryParams }],
            queryFn: () => getData(PRODUCT_CONTROL_API_URL, Entities.ATTRIBUTE_CATEGORY, {}),
        }),
    },
    imageDetails: {
        all: null,
        list: (listQueryParams: {}) => ({
            queryKey: [{ ...listQueryParams }],
            queryFn: () => getData(PRODUCT_CONTROL_API_URL, Entities.IMAGE_DETAILS, {}),
        }),
    },
    productSearch: {
        all: null,
        list: (listQueryParams: ProductSearchQueryProps) => ({
            queryKey: ['list-query-params', JSON.stringify(listQueryParams)],
            queryFn: () => createData(PRODUCT_CONTROL_API_URL, Entities.SEARCH_PRODUCT, listQueryParams),
        }),
    },
});

export default queryKeyStore;
