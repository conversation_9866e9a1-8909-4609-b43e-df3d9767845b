import React from 'react';
import {render} from '@testing-library/react';
import '@testing-library/jest-dom';
import {TreezThemeProvider} from '@treez-inc/component-library';
import {allColors} from '@treez-inc/component-library/dist/components/TreezThemeProvider/treez-colors';
import PageLayout, {PageLayoutProps} from '.';

const defaultProps = {
    children: <div />,
    testId: 'testId',
};

describe('<PageLayout />', () => {
    const renderPageLayoutComponent = (props: Partial<PageLayoutProps> = {}) => {
        const mergedProps = {...defaultProps, ...props};
        const {getByTestId} = render(
            <TreezThemeProvider>
                <PageLayout {...mergedProps} />
            </TreezThemeProvider>
        );
        const pageLayout = getByTestId(mergedProps.testId);
        return {pageLayout, getByTestId};
    };

    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    it('should render with correct styles', () => {
        const {pageLayout} = renderPageLayoutComponent();

        expect(pageLayout).toBeInTheDocument();
        expect(pageLayout).toHaveStyle('width: 100%');
        expect(pageLayout).toHaveStyle('height: 100%');
        expect(pageLayout).toHaveStyle('overflow-y: auto');
        expect(pageLayout).toHaveStyle(`background: ${allColors.primaryWhite.main}`);
    });

    it('should render children', () => {
        const children = <div data-testid="children" />;

        const {getByTestId} = renderPageLayoutComponent({children});

        expect(getByTestId('children')).toBeInTheDocument();
    });

    it('should render with the testId', () => {
        const testId = 'random-test-id';

        const {getByTestId} = renderPageLayoutComponent({testId});

        expect(getByTestId(testId)).toBeInTheDocument();
    });
});
