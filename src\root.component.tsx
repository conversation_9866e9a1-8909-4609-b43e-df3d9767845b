import React from 'react';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { TreezThemeProvider } from '@treez-inc/component-library';
import { QueryClient, QueryClientProvider } from 'react-query';
import IFrameworkProps from './interfaces/frameworkProps';
import ApiService from './utils/apiService';
import createRoutes from './utils/createRoutes';
import ApiProvider from './providers/ApiProvider';
import PermissionsProvider from './providers/PermissionsProvider';
import SnackbarProvider from './providers/SnackbarProvider';
import { setAuthTokensAccessor } from './api/genericAccessor';

const Root: React.FC<IFrameworkProps> = ({
    getTokens,
    refreshTokens,
    redirectToLogin,
    getPermissions,
}) => {
    const api = new ApiService(getTokens, refreshTokens, redirectToLogin);
    const routes = createRoutes();
    setAuthTokensAccessor(getTokens);
    const router = createBrowserRouter(routes);
    const queryClientProvider = new QueryClient();

    return (
        <React.StrictMode>
            <QueryClientProvider client={queryClientProvider}>
                <TreezThemeProvider>
                    <PermissionsProvider getPermissions={getPermissions}>
                        <SnackbarProvider>
                            <ApiProvider api={api}>
                                <RouterProvider router={router} />
                            </ApiProvider>
                        </SnackbarProvider>
                    </PermissionsProvider>
                </TreezThemeProvider>
            </QueryClientProvider>
        </React.StrictMode>
    );
};

export default Root;
