import React, {useEffect, useState} from 'react';
import {
    convertPxToRem,
    DataGridPro,
    ErrorTemplate,
    IconButton,
    Link,
    StaticChip,
    Tooltip,
} from '@treez-inc/component-library';
import {
    GridColDef,
    GridColumnHeaderParams,
    DataGridProProps,
    GridRowId,
    GridTreeNodeWithRender,
    useGridApiContext,
} from '@mui/x-data-grid-pro';
import {Typography, styled, Box, ButtonProps} from '@mui/material';
import {IProduct} from '../../interfaces/product';
import {IVariant} from '../../interfaces/variant';
import useBasePriceLabel from '../../hooks/useBasePriceLabel';
import useBulkAction from '../../hooks/useBulkAction';
import {COLLECTIONS_URL, PRODUCT_CONTROL_URL} from '../../constants/apiEndPoints';
import useApiContext from '../../hooks/useApiContext';
import useSnackbarContext from '../../hooks/useSnackbarContext';
import {getAllProductsData, removeSelectedProducts, removeVariantsAndExtractIds, selectedProductVariants} from '../../utils/filterUtil';
import {RemoveVariantsConfirmModal} from '../RemoveVariantsConfirmModel';
import { getVariantAmountLabel } from '../../utils/variantUtils';
import { VariantDto } from '../../interfaces/dto/variant';

interface ProductCollectionDetailsGridProps {
    data: IProduct[];
    refetch: () => void;
    loading: boolean;
    collectionId: string;
    collectionName: string;
    setIsLoading: (loading: boolean) => void;
}

const StyledCollectionsCount = styled(Typography)(() => ({
    paddingLeft: convertPxToRem(16),
}));

const StyledFilterContainer = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: '1rem',
}));

const ExpandableMenuContainer = styled(Box)(() => ({
    display: 'flex',
}));

const StyledGridColumn = styled(Box)`
    display: flex;
    width: 100%;
    justifycontent: 'center';
`;

const StyledGridColumnChip = styled(Box)(() => ({
    marginRight: convertPxToRem(4),
}));

const StyledErrorTemplateBox = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignContent: 'center',
    width: '100%',
    height: '100%',
}));

const StyledGridContainer = styled(Box)(() => ({
    width: '100%',
    paddingBottom: convertPxToRem(24),
    height: '100%',
}));

const headerConfig: Partial<GridColDef> = {
    renderHeader: (params: GridColumnHeaderParams) => (
        <Typography variant="mediumTextStrong" color="primaryBlackText">
            {params.colDef.headerName}
        </Typography>
    ),
};

const columns: GridColDef[] = [
    {
        ...headerConfig,
        flex: 0.7,
        field: 'productName',
        headerName: 'Product',
    },
    {
        ...headerConfig,
        flex: 0.5,
        field: 'sku',
        headerName: 'SKU',
    },
    {
        ...headerConfig,
        flex: 1,
        field: 'allSizes',
        headerName: 'Amount',
        sortable: false,
        renderCell: (params) => (
            <StyledGridColumn>
                {params?.row?.variants &&
                    params.row.variants.map((data: VariantDto, index: number) =>   
                        getVariantAmountLabel(data) && (
                        <StyledGridColumnChip
                            // eslint-disable-next-line react/no-array-index-key
                            key={`chip-${params.row.id}-${index}`}
                        >
                            <StaticChip
                                variant="filled"
                                testId="multiplestore-chip"
                                label={getVariantAmountLabel(data)}
                            />
                        </StyledGridColumnChip>
                    ))}
            </StyledGridColumn>
        ),
    },
    {
        ...headerConfig,
        flex: 0.5,
        field: 'brandName',
        headerName: 'Brand',
    },
    {
        ...headerConfig,
        flex: 0.5,
        field: 'productCategoryName',
        headerName: 'Category',
    },
    {
        ...headerConfig,
        flex: 0.5,
        field: 'productSubCategoryName',
        headerName: 'Subcategory',
    },
    {
        ...headerConfig,
        flex: 0.5,
        field: 'variants',
        headerName: 'Base Price',
        sortable: false,
        renderCell: (params) => (
            <StyledGridColumn>
                {params?.row?.variants && (
                    <Typography variant="mediumText">
                        {useBasePriceLabel(params?.row?.variants || [])}
                    </Typography>
                )}
            </StyledGridColumn>
        ),
    },
];

const productControlPageURL = `${PRODUCT_CONTROL_URL}`;
const NoProductsInCollectionResponse: React.FC = () => (
    <StyledErrorTemplateBox>
        <ErrorTemplate
            hideLogo
            title="This collection is empty"
            body={
                <p>
                    Go to &nbsp;
                    <Link
                        href={productControlPageURL}
                        ariaLabel="link that navigates to Product control page"
                    >
                        Products
                    </Link>
                    &nbsp;to add items in this collection
                </p>
            }
            testId="error-template-empty-collection-items"
        />
    </StyledErrorTemplateBox>
);

const CustomProductGroupingCell: React.FC<{
    id: GridRowId;
    field: string;
    row: IProduct;
    rowNode: GridTreeNodeWithRender;
}> = (props) => {
    const {id, field, rowNode, row} = props;

    const apiRef = useGridApiContext();
    const [isExpanded, setExpanded] = useState(false);

    const handleClick: ButtonProps['onClick'] = (event) => {
        if (rowNode.type !== 'group') {
            return;
        }

        apiRef.current.setRowChildrenExpansion(id, !rowNode.childrenExpanded);
        apiRef.current.setCellFocus(id, field);
        setExpanded(!rowNode.childrenExpanded);
        event.stopPropagation();
    };

    useEffect(() => {
        if (rowNode.type !== 'group') {
            return;
        }

        if (isExpanded !== rowNode.childrenExpanded) {
            apiRef.current.setRowChildrenExpansion(id, !rowNode.childrenExpanded);
            apiRef.current.setCellFocus(id, field);
        }
    }, [rowNode]);

    return (
        <ExpandableMenuContainer key={`product-grid-expandable-menu-container-${id}`}>
            {!row.isChild && (
                <IconButton
                    iconName={isExpanded ? 'ChevronUp' : 'ChevronRight'}
                    onClick={handleClick}
                    size="small"
                    testId="expand-product-row-button"
                    variant="secondary"
                />
            )}
        </ExpandableMenuContainer>
    );
};

const transformProductData = (productData: any) => {
    const updatedProductList: IProduct[] = [];
    productData?.forEach((productDataItem: IProduct) => {
        updatedProductList.push({
            ...productDataItem,
            hierarchy: [`disc-${productDataItem.productId}`],
            isChild: false,
            id: productDataItem.productId,
        });

        productDataItem?.variants?.forEach((variant: IVariant) => {
            if (variant) {
                const key = `store-ux-${variant.id}`;

                updatedProductList.push({
                    ...productDataItem,
                    variants: [variant],
                    hierarchy: [`disc-${productDataItem.productId}`, key],
                    isChild: true,
                    id: variant.id,
                    sku: variant?.sku,
                    productName: variant?.name,
                });
            }
        });
    });

    return updatedProductList;
};

const ProductCollectionDetailsGrid: React.FC<ProductCollectionDetailsGridProps> = ({
    data,
    refetch,
    loading,
    collectionId,
    collectionName,
    setIsLoading,
}) => {
    const [allProducts, setAllProducts] = useState<IProduct[]>([]);
    const [allProductsCount, setAllProductsCount] = useState<number>(Number);
    const [removeVariantsConfirmModalOpen, toggleRemoveVariantsConfirmModal] = useState<boolean>(false);
    const [isRemoveButtonDisabled, setRemoveButtonDisabled] = useState(false);
    const showConfirmation = () => toggleRemoveVariantsConfirmModal(true);
    
    const {api} = useApiContext();
    const {setSnackbar} = useSnackbarContext();

    const getTreeDataPath: DataGridProProps['getTreeDataPath'] = (row) => row.hierarchy;

    const getRowClassName: DataGridProProps['getRowClassName'] = (params) =>
        params.row.isChild ? 'DataGrid-Row-Child' : '';

    const groupingColDef: DataGridProProps['groupingColDef'] = {
        headerName: '',
        maxWidth: 70,
        renderCell: (params) => (
            <CustomProductGroupingCell
                id={params.id}
                rowNode={params.rowNode}
                field={params.field}
                row={params.row}
            />
        ),
        align: 'left',
    };

    useEffect(() => {
        if (data && data.length > 0) {
            const productsCount = data.filter((item) => !item.isChild).length || 0;
            setAllProductsCount(productsCount);
            setAllProducts(data);
        }
    }, [data]);

    const {
        selectionModel,
        handleSelectionModelChange,
        getBulkActionBarProps,
        selectedProductsCount,
    } = useBulkAction(allProducts);
    const bulkActionBarProps = getBulkActionBarProps({showConfirmation});

    const removeVariants = async () => {
        toggleRemoveVariantsConfirmModal(false);
        setRemoveButtonDisabled(false);
        setIsLoading(true);

        const {data: existingVariantsData} = await api.get(`${COLLECTIONS_URL}/product/${collectionId}`);

        const existingVariants = existingVariantsData?.data;
        const selectedVariants = await selectedProductVariants(selectionModel, allProducts);
        const filteredVariantIds = removeVariantsAndExtractIds(existingVariants, selectedVariants);

        const body = {
            name: collectionName,
            collectionItems: filteredVariantIds,
        };

        await api.put(`${COLLECTIONS_URL}/${collectionId}`, body).then((response) => {
            setTimeout(async () => {
                setSnackbar({
                    message: `Removed products from the collection ${response.data.name}.`,
                    severity: 'info',
                    iconName: 'Success',
                });

                const updatedProductsData = removeSelectedProducts(allProducts, selectionModel);
                setAllProducts(updatedProductsData);
                setAllProductsCount(getAllProductsData(updatedProductsData).length);
                setRemoveButtonDisabled(false);
                setIsLoading(false);
            }, 500);
        }).catch(()=>{
            setIsLoading(false);
            setSnackbar({
                message: `We can not remove products from ${collectionName} collection. Please try again later.`,
                severity: 'error',
                iconName: 'Error',
            });
            setRemoveButtonDisabled(false);
            setIsLoading(false);
        });
    };

    return (
        <>
            <StyledFilterContainer>
                <StyledCollectionsCount
                    variant="largeTextStrong"
                    data-testid="collection-products-count"
                >
                    {selectedProductsCount || allProductsCount || 0} Product
                    {(selectedProductsCount || allProductsCount) === 1 ? '' : 's'}
                    {selectedProductsCount > 0 ? ' Selected' : ''}
                </StyledCollectionsCount>
                
                <Tooltip
                title="A new tab on the Catalog Product page will open. After adding the products to the collection, return here and refresh the list."
                variant="multiRow"
                >
                    <Link
                    underline="always"
                    colorVariant="primary"
                    variant="largeTextStrong"
                    href="/product-control"
                    newTab
                    testId="add-new-product-to-collection-link"
                    ariaLabel="Add New Products to Collection"
                    >
                    + Add New Products to Collection
                    </Link>
                </Tooltip>

                <IconButton
                    iconName="Sync"
                    size="small"
                    onClick={() => refetch()}
                />
            </StyledFilterContainer>
            <StyledGridContainer>
                {allProducts && (
                    <DataGridPro
                        loading={loading}
                        columns={columns}
                        rowCount={(allProductsCount as any) || 0}
                        rows={(allProducts as any) || []}
                        rowSpacingType="border"
                        paginationMode="client"
                        columnBuffer={5}
                        getRowId={(row: IProduct) => row?.id}
                        treeData
                        getTreeDataPath={getTreeDataPath}
                        getRowClassName={getRowClassName}
                        groupingColDef={groupingColDef}
                        slots={{
                            noRowsOverlay: NoProductsInCollectionResponse,
                            noResultsOverlay: NoProductsInCollectionResponse,
                        }}
                        checkboxSelection
                        rowSelectionModel={selectionModel}
                        onRowSelectionModelChange={handleSelectionModelChange}
                        slotProps={{
                            toolbar: {
                                ...(bulkActionBarProps.buttonProps.length && {bulkActionBarProps}),
                            },
                        }}
                        hideFooterSelectedRowCount
                    />
                )}
                <RemoveVariantsConfirmModal
                    allProducts={allProducts}
                    removeVariants={removeVariants}
                    toggleRemoveVariantsConfirmModal={toggleRemoveVariantsConfirmModal}
                    removeVariantsConfirmModalOpen={removeVariantsConfirmModalOpen}
                    collectionName={collectionName}
                    selectionModel={selectionModel}
                    selectedProductsCount={selectedProductsCount}
                    isRemoveButtonDisabled={isRemoveButtonDisabled}
                />
            </StyledGridContainer>
        </>
    );
};

export {transformProductData, CustomProductGroupingCell, ProductCollectionDetailsGrid};
