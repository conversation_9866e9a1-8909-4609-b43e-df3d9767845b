import React, { MutableRefObject } from 'react';
import '@testing-library/jest-dom';
import { TreezThemeProvider } from '@treez-inc/component-library';
import { fireEvent, render, waitFor, } from '@testing-library/react';
import * as ReactQuery from 'react-query';
import { QueryObserverSuccessResult, QueryClient, QueryClientProvider } from 'react-query';
import SnackbarProvider from '../../../providers/SnackbarProvider';
import ProductList from ".";
import ApiService from '../../../utils/apiService';


jest.mock('../../../hooks/usePermissionsContext');
jest.mock('../../../hooks/useApiContext');
jest.mock('../../../hooks/useSnackbarContext');

const mockUseApiContext = require('../../../hooks/useApiContext').default;
const mockUseSnackbarContext = require('../../../hooks/useSnackbarContext').default;

const selectedCategory = "83d31f80-65d1-4aa8-bb63-37cf9ec81b0a";
const selectedRulesMock = jest.fn();
const queryClientProvider = new QueryClient();
const validTokens = {
    accessToken: 'accessToken',
    expiresIn: 300,
    refreshToken: 'refreshToken',
    idToken: 'idToken',
  };
  const clearTokens = jest.fn();
  const getTokens = () => validTokens;
  const redirectToLogin = jest.fn();
  const apiService = new ApiService(getTokens, clearTokens, redirectToLogin);

const emptyProductList = {
    "status": "success",
    "isLoading": false,
    "isSuccess": true,
    "isError": false,
    "isIdle": false,
    "data": {
        "data": [],
        "totalRecords": 0,
        "page": 0,
        "pageSize": 20
    },
    "dataUpdatedAt": 1709893518961,
    "error": null,
    "errorUpdatedAt": 0,
    "failureCount": 0,
    "errorUpdateCount": 0,
    "isFetched": true,
    "isFetchedAfterMount": true,
    "isFetching": false,
    "isRefetching": false,
    "isLoadingError": false,
    "isPlaceholderData": false,
    "isPreviousData": false,
    "isRefetchError": false,
    "isStale": true
} as QueryObserverSuccessResult;

const productsList = {
    "status": "success",
    "isLoading": false,
    "isSuccess": true,
    "isError": false,
    "isIdle": false,
    "data": {
        "data": [
            {
                "productId": "4ef5cf35-0d9a-4397-94fe-614b161053d1",
                "verifiedReferenceId": null,
                "productName": "Test Product P1",
                "brandId": "eca27b78-07ac-4b21-ace6-bfbbe6df91b6",
                "brandName": "Pour Twenty",
                "productSubCategoryId": "6f90d74f-48da-49ba-8365-9482c60fa254",
                "productSubCategoryName": "Beer",
                "productCategoryId": "1e727c61-182c-439c-92f0-2d28c7fc12bg",
                "productCategoryName": "Beverage",
                "classification": "Sativa",
                "status": "active",
                "lastUpdated": "2024-03-05T05:49:07.942Z",
                "organizationId": "8ca822f9-4915-41db-a9ba-9d7eb63cad34",
                "variants": [
                    {
                        "id": "cd7ba554-1c37-4d9c-9c34-e5dcf2f9b27e",
                        "defaultPrices": {},
                        "status": "active",
                        "details": {
                            "isPromo": false,
                            "isSample": false,
                            "hideFromEcomMenu": false
                        },
                        "updatedAt": "2024-03-05T05:49:41.482646+00:00"
                    },
                    {
                        "id": "cd7ba554-1c37-4d9c-9c34-e5dcf3f9b28e",
                        "defaultPrices": {},
                        "status": "active",
                        "details": {
                            "isPromo": true,
                            "isSample": false,
                            "hideFromEcomMenu": false
                        },
                        "updatedAt": "2024-03-05T05:49:41.482646+00:00"
                    },
                    {
                        "id": "cd7ba554-1c37-4d9c-9d34-e5dcf2f9b28f",
                        "defaultPrices": {},
                        "status": "active",
                        "details": {
                            "isPromo": false,
                            "isSample": true,
                            "hideFromEcomMenu": false
                        },
                        "updatedAt": "2024-03-05T05:49:41.482646+00:00"
                    }
                ],
                "allSizes": "1",
            },
            {
                "productId": "4ef5df35-0d9a-4397-94fe-614b161053d0",
                "verifiedReferenceId": null,
                "productName": "newProduct",
                "brandId": "eca27b78-07ac-3b21-ace6-bfbbe6df91b5",
                "brandName": "Pour Twenty",
                "productSubCategoryId": "9f90d74f-48da-49ba-8365-9482c60fa253",
                "productSubCategoryName": "Beer",
                "productCategoryId": "1e727c61-182c-439c-92f0-2d28c7fc12bf",
                "productCategoryName": "Beverage",
                "classification": "Sativa",
                "status": "active",
                "lastUpdated": "2024-03-05T05:49:07.942Z",
                "organizationId": "9ca822f9-4915-41db-a9ba-9d7eb63cad33",
                "variants": [
                    {
                        "id": "cd7ba554-1c38-4d9c-9c34-e5dcf2f9b27f",
                        "defaultPrices": {},
                        "status": "active",
                        "details": {
                            "isPromo": false,
                            "isSample": false,
                            "hideFromEcomMenu": false
                        },
                        "updatedAt": "2024-03-05T05:49:41.482646+00:00"
                    },
                    {
                        "id": "cd7ba554-1c35-4d9c-9c34-e5dcf2f9b27e",
                        "defaultPrices": {},
                        "status": "active",
                        "details": {
                            "isPromo": true,
                            "isSample": false,
                            "hideFromEcomMenu": false
                        },
                        "updatedAt": "2024-03-05T05:49:41.482646+00:00"
                    },
                    {
                        "id": "cd7ba554-1c37-4d0c-9c34-e5dcf2f9b27f",
                        "defaultPrices": {},
                        "status": "active",
                        "details": {
                            "isPromo": false,
                            "isSample": true,
                            "hideFromEcomMenu": false
                        },
                        "updatedAt": "2024-03-05T05:49:41.482646+00:00"
                    }
                ],
                "allSizes": "1",
            },
        ],
        "totalRecords": 2,
        "page": 0,
        "pageSize": 1
    },
    "dataUpdatedAt": 1709893518961,
    "error": null,
    "errorUpdatedAt": 0,
    "failureCount": 0,
    "errorUpdateCount": 0,
    "isFetched": true,
    "isFetchedAfterMount": true,
    "isFetching": false,
    "isRefetching": false,
    "isLoadingError": false,
    "isPlaceholderData": false,
    "isPreviousData": false,
    "isRefetchError": false,
    "isStale": true
} as QueryObserverSuccessResult;

const useQueryMock = jest.spyOn(ReactQuery, 'useQuery').mockReturnValue(productsList);
const excludedVariantIdsMock: string[] = [];
const setExcludedVariantIdsMock = jest.fn();
const excludeSelectionCheckedRefMock: MutableRefObject<boolean> = { current: false };
const collectionDetailsDataMock = jest.fn();
const mockSetSnackbar = jest.fn();

describe('<ProductList/>', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    
        mockUseApiContext.mockImplementation(() => apiService);
        mockUseSnackbarContext.mockReturnValue({
          setSnackbar: mockSetSnackbar,
        });
      });

    it('ProductList renders properly with empty data', () => {
        useQueryMock.mockReturnValue(emptyProductList)
        const { getByText } = render(
            <QueryClientProvider client={queryClientProvider}>
                <TreezThemeProvider>
                    <SnackbarProvider>
                        <ProductList
                            selectedCategory={selectedCategory}
                            selectedRules={selectedRulesMock}
                            excludedVariantIds={excludedVariantIdsMock}
                            setExcludedVariantIds={setExcludedVariantIdsMock}
                            excludeSelectionCheckedRef={excludeSelectionCheckedRefMock}
                            collectionDetailsData={collectionDetailsDataMock}
                        />
                    </SnackbarProvider>
                </TreezThemeProvider>
            </QueryClientProvider>
        );
        expect(getByText("No product(s) found for the applied filter(s)")).toBeInTheDocument();
    });

    it('ProductList renders properly with data', () => {
        useQueryMock.mockReturnValue(productsList);
        const { getByTestId } = render(
            <QueryClientProvider client={queryClientProvider}>
                <TreezThemeProvider>
                    <SnackbarProvider>
                        <ProductList
                            selectedCategory={selectedCategory}
                            selectedRules={selectedRulesMock}
                            excludedVariantIds={excludedVariantIdsMock}
                            setExcludedVariantIds={setExcludedVariantIdsMock}
                            excludeSelectionCheckedRef={excludeSelectionCheckedRefMock}
                            collectionDetailsData={collectionDetailsDataMock}
                        />
                    </SnackbarProvider>
                </TreezThemeProvider>
            </QueryClientProvider>
        );
        const listContainer = getByTestId('product-list-container');
        expect(listContainer).toBeInTheDocument();
    });

    it('Should be able to select products', () => {
        useQueryMock.mockReturnValue(productsList);
        const { getAllByRole, getByText } = render(
            <QueryClientProvider client={queryClientProvider}>
            <TreezThemeProvider>
                <SnackbarProvider>
                    <ProductList
                        selectedCategory={selectedCategory}
                        selectedRules={selectedRulesMock}
                        excludedVariantIds={excludedVariantIdsMock}
                        setExcludedVariantIds={setExcludedVariantIdsMock}
                        excludeSelectionCheckedRef={excludeSelectionCheckedRefMock}
                        collectionDetailsData={collectionDetailsDataMock}
                    />
                </SnackbarProvider>
            </TreezThemeProvider>
            </QueryClientProvider>
        );
        const checkboxes = getAllByRole("checkbox");
        fireEvent.click(checkboxes[1]);
        expect(getByText("Test Product P1")).toBeInTheDocument();
    });

    it('Should be able to select multiple products ', () => {
        useQueryMock.mockReturnValue(productsList);
        const { getAllByRole, getByText } = render(
            <QueryClientProvider client={queryClientProvider}>
                <TreezThemeProvider>
                    <SnackbarProvider>
                        <ProductList
                            selectedCategory={selectedCategory}
                            selectedRules={selectedRulesMock}
                            excludedVariantIds={excludedVariantIdsMock}
                            setExcludedVariantIds={setExcludedVariantIdsMock}
                            excludeSelectionCheckedRef={excludeSelectionCheckedRefMock}
                            collectionDetailsData={collectionDetailsDataMock}
                        />
                    </SnackbarProvider>
                </TreezThemeProvider>
            </QueryClientProvider>
        );
        const checkboxes = getAllByRole("checkbox");
        fireEvent.click(checkboxes[1]);
        fireEvent.click(checkboxes[0]);
        expect(getByText("Test Product P1")).toBeInTheDocument();
    });

    it('Should be able to search product ', async () => {
        useQueryMock.mockReturnValue(productsList);
        const { queryByPlaceholderText } = render(
            <QueryClientProvider client={queryClientProvider}>
                <TreezThemeProvider>
                    <SnackbarProvider>
                        <ProductList
                            selectedCategory={selectedCategory}
                            selectedRules={selectedRulesMock}
                            excludedVariantIds={excludedVariantIdsMock}
                            setExcludedVariantIds={setExcludedVariantIdsMock}
                            excludeSelectionCheckedRef={excludeSelectionCheckedRefMock}
                            collectionDetailsData={collectionDetailsDataMock}
                        />
                    </SnackbarProvider>
                </TreezThemeProvider>
            </QueryClientProvider>
        );
        const searchTextBar = queryByPlaceholderText('Search…');
        expect(searchTextBar).toBeInTheDocument();
        if (searchTextBar) {
            fireEvent.change(searchTextBar, { target: { value: 'newProduct' } });
            await waitFor(() => {
                expect(searchTextBar).toHaveValue('newProduct');
            });
        }
    });

    it('Should be able to sort product ', () => {
        useQueryMock.mockReturnValue(productsList);
        const { getByText } = render(
            <QueryClientProvider client={queryClientProvider}>
                <TreezThemeProvider>
                    <SnackbarProvider>
                        <ProductList
                            selectedCategory={selectedCategory}
                            selectedRules={selectedRulesMock}
                            excludedVariantIds={excludedVariantIdsMock}
                            setExcludedVariantIds={setExcludedVariantIdsMock}
                            excludeSelectionCheckedRef={excludeSelectionCheckedRefMock}
                            collectionDetailsData={collectionDetailsDataMock}
                        />
                    </SnackbarProvider>
                </TreezThemeProvider>
            </QueryClientProvider>
        );
        const sortButtons = getByText("Amount");
        fireEvent.click(sortButtons);
        fireEvent.click(sortButtons);
        expect(getByText("Test Product P1")).toBeInTheDocument();
    });

    it.skip('Should be able to paginate product control table ', () => {
        const products = Array.from(Array(21)).map((key: number, index: number) => ({
            productId: index,
            name: `test${index}`,
        }))
        useQueryMock.mockReturnValue({
            ...productsList,
            data: {
                data: products,
                "totalRecords": 21,
                "page": 0,
                "pageSize": 2
            }
        });
        
        const excludedVariantIds = Array.from({ length: 21 }, (_, i) => i);
        
        const { getByLabelText } = render(
            <QueryClientProvider client={queryClientProvider}>
                <TreezThemeProvider>
                    <SnackbarProvider>
                        <ProductList
                            selectedCategory={selectedCategory}
                            selectedRules={selectedRulesMock}
                            excludedVariantIds={excludedVariantIds}
                            setExcludedVariantIds={setExcludedVariantIdsMock}
                            excludeSelectionCheckedRef={excludeSelectionCheckedRefMock}
                            collectionDetailsData={collectionDetailsDataMock}
                        />
                    </SnackbarProvider>
                </TreezThemeProvider>
            </QueryClientProvider>
        );
        const paginationButton = getByLabelText('Go to page 2');
        fireEvent.click(paginationButton);
        expect(paginationButton).toBeInTheDocument();
    });
});


