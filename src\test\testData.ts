export const tokenData = {
    accessToken: 'accessToken',
    expiresIn: 300,
    refreshToken: 'refreshToken',
    idToken: 'idToken',
};

export const productCollectionListData = {
    totalCount: 2,
    data: [
        {
            id: '91787d3b-f7c1-4f99-a25e-b021ba899e9d',
            name: 'Collection1',
            organizationId: '8ca822f9-4915-41db-a9ba-9d7eb63cad33',
            sync: false,
            createdAt: '2023-11-06T12:55:27.527Z',
            updatedAt: '2023-11-06T12:55:57.527Z',
            deletedAt: null,
        },
        {
            id: '91787d3b-f7c1-4f99-a25e-b021ba899e3d',
            name: 'Collection2',
            organizationId: '8ca822f9-4915-41db-a9ba-9d7eb63cad33',
            sync: false,
            createdAt: '2023-11-06T12:55:27.527Z',
            updatedAt: '2023-11-06T12:55:57.527Z',
            deletedAt: null,
        },
        {
            id: 'a376217e-3084-4610-a6fe-c3d192d34af6',
            name: 'Collection3',
            organizationId: '8ca822f9-4915-41db-a9ba-9d7eb63cad33',
            sync: false,
            createdAt: '2023-11-06T12:50:03.584Z',
            updatedAt: '2023-11-06T12:55:03.584Z',
            deletedAt: '2023-11-06T12:55:03.584Z',
        },
        {
            id: '2a92eb35-c469-4054-ac0b-81dc9beceb07',
            name: 'Automated Collection',
            organizationId: '8ca822f9-4915-41db-a9ba-9d7eb63cad33',
            sync: true,
            createdAt: '2023-11-06T12:50:03.584Z',
            updatedAt: '2023-11-06T12:55:03.584Z',
            deletedAt: null,
        },
    ],
};

export const productListData = {
    data: [
        {
            id: '7c2ea386-923e-4801-aaaf-9a5d2d4e450a',
            productId: '7c2ea386-923e-4801-aaaf-9a5d2d4e450a',
            productName: 'Product A',
            sku: 'Test SKU',
            brandId: '1de6e11c-e915-43c0-8e76-3dd332cdf5ed',
            brandName: 'test',
            productSubCategoryId: '790f86a7-c99b-4e21-8f49-6843432f1b4c',
            productSubCategoryName: 'Chewable',
            productCategoryId: '567ac78f-0e97-4703-aee3-637cb2dac490',
            productCategoryName: 'Pill',
            status: 'active',
            hierarchy: [''],
            variants: [
                {
                    id: 'eef22692-3a97-4b3f-89c2-08bba6dd3ce8',
                    name: 'dummy variant',
                    isExclude: false,
                    defaultPrices: {
                        base: 800,
                    },
                    unitCount: 1,
                    amount: 1,
                    uom: 'Pack',
                    merchandiseSize: 'XL',
                    sku: 'test',
                },
                {
                    id: 'f3c64d2f-a439-48dc-9042-90f045a0916a',
                    name: 'dummy variant',
                    isExclude: false,
                    defaultPrices: {
                        base: 400,
                    },
                    unitCount: 1,
                    amount: 1,
                    uom: 'Pack',
                    merchandiseSize: 'XL',
                    sku: 'test',
                },
            ],
            allSizes: '2',
            collectionId: 'e1cac016-0877-4e93-8532-334dadeb9daa',
            collectionName: 'Collection A',
        },
        {
            id: '83d519f1-dc34-4d49-a099-f8dd4d9bdaab',
            productId: '83d519f1-dc34-4d49-a099-f8dd4d9bdaab',
            productName: 'Product B',
            sku: 'Test SKU',
            brandId: '7cd11874-f905-4378-abeb-58866aaad15a',
            brandName: 'Mako',
            productSubCategoryId: 'f83d73cd-54ba-4074-81ed-e69e9ed3ddd7',
            productSubCategoryName: 'Tablet',
            productCategoryId: '567ac78f-0e97-4703-aee3-637cb2dac490',
            productCategoryName: 'Pill',
            status: 'active',
            hierarchy: [''],
            variants: [
                {
                    id: '358c7c40-1cfb-4c19-8fda-a3f06e316c93',
                    name: 'dummy variant',
                    isExclude: false,
                    defaultPrices: {
                        base: 2000,
                    },
                    unitCount: 1,
                    amount: 1,
                    uom: 'Pack',
                    merchandiseSize: 'XL',
                    sku: 'test',
                },
                {
                    id: '23bc9e74-164f-4752-9f69-7a2aa0951bbc',
                    name: 'dummy variant',
                    isExclude: false,
                    defaultPrices: {},
                    unitCount: 1,
                    amount: 1,
                    uom: 'Pack',
                    merchandiseSize: 'XL',
                    sku: 'test',
                },
            ],
            allSizes: '2',
            collectionId: 'e1cac016-0877-4e93-8532-334dadeb9daa',
            collectionName: 'Collection A',
        },
    ],
};
