import React, {useMemo, createContext} from 'react';
import ApiService from '../../utils/apiService';

export const ApiContext = createContext<{api: ApiService} | null>(null);

export interface ApiProviderProps {
    children: React.ReactNode;
    api: ApiService;
}

const ApiProvider: React.FC<ApiProviderProps> = ({children, api}) => {
    const provider = useMemo(() => ({api}), [api]);

    return <ApiContext.Provider value={provider}>{children}</ApiContext.Provider>;
};

export default ApiProvider;
