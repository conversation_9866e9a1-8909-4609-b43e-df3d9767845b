import {renderHook, waitFor} from '@testing-library/react';
import useGetRequest from '.';

const mockApi = {
    get: jest.fn(),
};

jest.mock('../useApiContext');
const mockApiContext = require('../useApiContext').default;

const apiUrl = 'apiUrl';

describe('useGetRequest()', () => {
    beforeEach(() => {
        mockApiContext.mockReturnValue({api: mockApi});
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    it('should throw an error when the GET request fails', async () => {
        const expectedError = new Error();
        mockApi.get.mockImplementation(() => {
            throw expectedError;
        });

        const {result} = renderHook(() => useGetRequest(apiUrl));

        await waitFor(() => {
            expect(mockApi.get).toHaveBeenCalledTimes(1);
            expect(mockApi.get).toHaveBeenCalledWith(apiUrl);
            expect(result.current.error).toEqual(expectedError);
            expect(result.current.loading).toEqual(false);
            expect(result.current.data).toEqual(null);
        });
    });

    it('should return data successfully from the api', async () => {
        const expectedData = 'data';
        mockApi.get.mockResolvedValue({data: expectedData});

        const {result} = renderHook(() => useGetRequest(apiUrl));

        await waitFor(() => {
            expect(mockApi.get).toHaveBeenCalledTimes(1);
            expect(mockApi.get).toHaveBeenCalledWith(apiUrl);
            expect(result.current.error).toEqual(null);
            expect(result.current.loading).toEqual(false);
            expect(result.current.data).toEqual(expectedData);
        });
    });
});
