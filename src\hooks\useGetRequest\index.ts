import {useEffect, useReducer} from 'react';
import useApiContext from '../useApiContext';

const LOADING_STATUS = 'LOADING' as const;
const SUCCESS_STATUS = 'SUCCESS' as const;
const FAILURE_STATUS = 'FAILURE' as const;

type ErrorType = unknown;

type State<Data> =
    | {loading: boolean; error: null; data: null}
    | {loading: false; error: ErrorType; data: null}
    | {loading: false; error: null; data: Data};

type Action<Data> =
    | {type: typeof LOADING_STATUS}
    | {type: typeof SUCCESS_STATUS; payload: Data}
    | {type: typeof FAILURE_STATUS; payload: ErrorType};

export function stateReducer<Data>(_: State<Data>, action: Action<Data>): State<Data> {
    switch (action.type) {
        case LOADING_STATUS:
            return {loading: true, error: null, data: null};
        case SUCCESS_STATUS:
            return {
                error: null,
                loading: false,
                data: action.payload as Data,
            };
        default:
            return {
                error: action.payload as ErrorType,
                loading: false,
                data: null,
            };
    }
}

export type UseGetRequestState<Data> = State<Data> & {
    refetch: () => void;
};

export const initialState: UseGetRequestState<never> = {
    loading: true,
    error: null,
    data: null,
    refetch: () => {},
};

export default function useGetRequest<Data>(endpoint: string): UseGetRequestState<Data> {
    const [state, dispatch] = useReducer(stateReducer<Data>, initialState);
    const {api} = useApiContext();

    const getDataAsync = async () => {
        dispatch({type: LOADING_STATUS});
        try {
            const {data} = await api.get(endpoint);
            dispatch({type: SUCCESS_STATUS, payload: data});
        } catch (error) {
            dispatch({
                type: FAILURE_STATUS,
                payload: error,
            });
        }
    };

    useEffect(() => {
        getDataAsync();
    }, []);

    return {...state, refetch: getDataAsync};
}
