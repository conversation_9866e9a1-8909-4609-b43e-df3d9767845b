import { IconName } from '@treez-inc/component-library/dist/components/Icon/types';
import { DetailsDto } from './details';
import { BrandDto } from './brand';
import { ProductSubCategoryDto } from './productSubCategory';
import { ImageDetailsDto, ReferenceIdDto, VariantDto } from './variant';

export interface ProductDto {
    id?: string;
    category?: IconName;
    brand?: BrandDto;
    brandId?: string;
    createdAt?: string;
    deletedAt?: null | string;
    details?: DetailsDto;
    images?: ImageDetailsDto[];
    status: string;
    name: string;
    organizationId?: string;
    productAttributes?: [];
    productSubCategory?: ProductSubCategoryDto;
    productSubCategoryId?: string;
    referenceIds?: ReferenceIdDto[];
    strain?: string;
    uom?: string;
    updatedAt?: string;
    variants?: VariantDto[];
    verifiedReferenceId?: string;
}

export interface ProductSearchResponse {
    id: string;
    productId: string;
    productName: string;
    verifiedReferenceId?: string;
    brandId: string;
    brandName: string;
    productSubCategoryId: string;
    productSubCategoryName: string;
    productCategoryId: string;
    productCategoryName: string;
    classification: string;
    images: ImageDetailsDto[];
    status: string;
    lastUpdated: string;
    organizationId: string;
    allSizes: string;
    hierarchy: string[];
    isChild: boolean;
    variants: VariantDto[];
    formattedVariantSizes: string[];
}

export interface ProductMergeDto {
    productIds: string[];
    targetMergeProductId: string;
}
