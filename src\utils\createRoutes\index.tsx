import React from 'react';
import {RouteObject} from 'react-router-dom';
import PermissionedRoute from '../../components/PermissionedRoute';
import ErrorDefault from '../../components/ErrorDefault';
import ErrorNotFound from '../../components/ErrorNotFound';
import {basePath} from '../../constants/routes';
import {MFEPermissions} from '../../interfaces/permissions';
import ProductCollection from '../../views/ProductCollection';
import ProductCollectionDetails from '../../views/ProductCollectionDetails';
import AutomatedCollectionForm from '../../views/AutomatedCollectionForm';

const createRoutes = (): RouteObject[] => [
    {
        path: basePath,
        errorElement: <ErrorDefault />,
        children: [
            {
                index: true,
                element: (
                    <PermissionedRoute
                        anyOf={[
                            MFEPermissions.VIEW_PRODUCT_COLLECTIONS,
                            MFEPermissions.MANAGE_PRODUCT_COLLECTIONS,
                        ]}
                    >
                        <ProductCollection />
                    </PermissionedRoute>
                ),
            },
            {
                path: `${basePath}/:id`,
                element: (
                    <PermissionedRoute
                        anyOf={[
                            MFEPermissions.VIEW_PRODUCT_COLLECTIONS,
                            MFEPermissions.MANAGE_PRODUCT_COLLECTIONS,
                        ]}
                    >
                        <ProductCollectionDetails />
                    </PermissionedRoute>
                ),
            },
            {
                path: `${basePath}/automated/add`,
                element: (
                    <PermissionedRoute
                        anyOf={[
                            MFEPermissions.VIEW_PRODUCT_COLLECTIONS,
                            MFEPermissions.MANAGE_PRODUCT_COLLECTIONS,
                        ]}
                    >
                        <AutomatedCollectionForm />
                    </PermissionedRoute>
                ),
            },
            {
                path: `${basePath}/automated/:id`,
                element: (
                    <PermissionedRoute
                        anyOf={[
                            MFEPermissions.VIEW_PRODUCT_COLLECTIONS,
                            MFEPermissions.MANAGE_PRODUCT_COLLECTIONS,
                        ]}
                    >
                        <AutomatedCollectionForm />
                    </PermissionedRoute>
                ),
            },
            {
                path: '*',
                element: <ErrorNotFound />,
            },
        ],
    },
    {
        path: '*',
        element: <ErrorNotFound />,
    },
];

export default createRoutes;
