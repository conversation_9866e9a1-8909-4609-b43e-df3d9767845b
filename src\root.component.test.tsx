import React from 'react';
import {render} from '@testing-library/react';
import Root from './root.component';

const mockApi = jest.fn();
jest.mock('./utils/apiService');
jest.mock('./utils/createRoutes');
jest.mock('./providers/PermissionsProvider');

const mockApiService = require('./utils/apiService').default;
const mockCreateRoutes = require('./utils/createRoutes').default;

const frameworkProps = {
    getTokens: jest.fn(),
    clearTokens: jest.fn(),
    refreshTokens: jest.fn(),
    redirectToLogin: jest.fn(),
    getPermissions: jest.fn(),
};

describe('Root component', () => {
    it('should render', async () => {
        mockApiService.mockImplementation(() => mockApi);
        mockCreateRoutes.mockReturnValue([
            {
                path: '/',
                element: <div />,
            },
        ]);

        render(<Root {...frameworkProps} />);

        expect(mockApiService).toHaveBeenCalledTimes(1);
        expect(mockApiService).toHaveBeenCalledWith(
            frameworkProps.getTokens,
            frameworkProps.refreshTokens,
            frameworkProps.redirectToLogin
        );
        expect(mockCreateRoutes).toHaveBeenCalledTimes(1);
    });
});
