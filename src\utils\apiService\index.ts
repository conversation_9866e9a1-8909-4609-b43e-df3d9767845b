import {AxiosRequestConfig} from 'axios';
import apiInterceptor from '../apiInterceptor';
import IFrameworkProps from '../../interfaces/frameworkProps';

export default class ApiService {
    getTokens;

    api;

    constructor(
        getTokens: IFrameworkProps['getTokens'],
        refreshTokens: IFrameworkProps['refreshTokens'],
        redirectToLogin: IFrameworkProps['redirectToLogin']
    ) {
        this.getTokens = getTokens;
        this.api = apiInterceptor(this.getTokens, refreshTokens, redirectToLogin);
    }

    addHeaders(): AxiosRequestConfig {
        const {accessToken} = this.getTokens();
        return {
            headers: {
                Authorization: accessToken,
                retry: '0',
            },
        };
    }

    async get(url: string) {
        return this.api.get(url, this.addHeaders());
    }

    async post(url: string, payload: unknown) {
        return this.api.post(url, payload, this.addHeaders());
    }

    async put(url: string, payload: unknown) {
        return this.api.put(url, payload, this.addHeaders());
    }

    async delete(url: string) {
        return this.api.delete(url, this.addHeaders());
    }
}
