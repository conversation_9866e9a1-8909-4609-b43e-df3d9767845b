import { DefaultPricesDto } from './defaultPrices';

export interface ReferenceIdDto {
    sourceId: string;
    sourceName: string;
}

export interface ImageDetailsDto {
    id?: string;
    name?: string;
    description?: string;
    order?: number;
    imageId?: string;
    productId?: string;
    variantId?: string;
    organizationId?: string;
    imageUrl?: string;
    verifiedReferenceId?: string;
}

export interface VariantDto {
    defaultPrices?: DefaultPricesDto;
    details?: VariantDetailsDto;
    id?: string;
    parentId?: string;
    productId: string;
    organizationId?: string;
    referenceIds?: ReferenceIdDto[];
    sku?: string;
    additionalSku?: string[];
    images?: ImageDetailsDto[];
    status: string;
    children?: VariantDto[];
    verifiedReferenceId?: string;
    updatedAt?: string;
    uom: string | null;
    unitCount: number;
    amount: number | null;
    merchandiseSize?: string | null;
    name: string;
}

export interface VariantDetailsDto {
    cbdPerDose?: number;
    description?: string;
    doses?: number;
    extractionMethod?: string;
    grossWeight?: number;
    hideFromEcomMenu?: boolean;
    isPromo?: boolean;
    isSample?: boolean;
    menuTitle?: string;
    name?: string; // old field, replaced by menuTitle
    netWeight?: number;
    netWeightUom?: string;
    thcPerDose?: number;
    totalConcentrateWeight?: number;
    totalFlowerWeight?: number;
    totalMgCbd?: number;
    totalMgThc?: number;
    usableMarijuanaWeight?: number;
    useGlobalDescription?: boolean;
}

export interface VariantPropertiesDto {
    size?: number;
    merchandiseSize?: string;
    sizeLabel: string;
}

export interface VariantFormObjectDto extends Omit<VariantDto, 'defaultPrices' | 'details' | 'name'>, VariantDetailsDto {
    chkReferenceId: boolean;
    isExisting: boolean;
    productCategoryName?: string;
}
