const service = 'product-collection';

export const getCypressEnv = (env: 'sandbox' | 'build') => {
    const subdomain = env === 'build' ? 'dev' : ('sandbox' as const);
    return {
        authenticationApiUrl:
            process.env.CYPRESS_AUTHN_API_URL || `https://api.${subdomain}.treez.io/auth-v2`,
        productCollectionApiUrl:
            process.env.CYPRESS_PRODUCT_COLLECTION_API_URL ||
            `https://api.${subdomain}.treez.io/product-collection/v1`,
        users: {
            admin: {
                username:
                    process.env.CYPRESS_ADMIN_USERNAME ||
                    `qa-tester+${subdomain}-${service}@treez.io`,
            },
            nonAdmin: {
                username:
                    process.env.CYPRESS_NON_ADMIN_USERNAME ||
                    `qa-tester+${subdomain}-${service}@treez.io`,
            },
        },
    };
};
