import {v4 as uuid} from 'uuid';
import {TUser} from './types';
import ITokens from '../../src/interfaces/tokens';
import {CollectionCreateDTO} from '../../src/interfaces/productCollection';

const {authenticationApiUrl, users, productCollectionApiUrl} = Cypress.env(Cypress.env('stage'));
export {productCollectionApiUrl};

export const getTokens = () => {
    const tokens = window.localStorage.getItem('tz-tokens');
    if (tokens === null) {
        throw new Error('No tokens found');
    }
    return JSON.parse(tokens) as ITokens;
};

Cypress.on('uncaught:exception', (e) => {
    if (e.message.includes(`Cannot read properties of null (reading 'accessToken')`)) {
        // we expected this error from the ui core, so let's ignore it
        // and let the test continue
        return false;
    }
    // on any other error message the test fails
});

Cypress.Commands.add('login', (username, password) => {
    cy.request({
        method: 'POST',
        url: `${authenticationApiUrl}/login`,
        body: Buffer.from(
            JSON.stringify({
                username,
                password,
            })
        ).toString('base64'),
        headers: {
            'x-application': 'MSO',
        },
    })
        .its('body')
        .then((body) => {
            window.localStorage.setItem('tz-tokens', JSON.stringify(body.tokens));
        });
});

Cypress.Commands.add('loginAs', (userType: TUser) => {
    const username = users[userType].username;
    cy.task('getPassword', username).then(({error, password}: any) => {
        if (error !== null) {
            cy.log(`Error getting password: ${JSON.stringify(error)}`);
        } else {
            cy.login(username, password);
        }
    });
});

Cypress.Commands.add('createCollection', (payload?: CollectionCreateDTO) => {
    cy.log('@create collection');
    cy.request({
        method: 'POST',
        url: productCollectionApiUrl,
        headers: {
            Authorization: getTokens().accessToken,
        },
        body: {
            name: payload?.name || `E2E Test - ${uuid()}`,
        },
    }).then((resp) => {
        expect(resp.status).to.eq(200);
        cy.log('collection created');
        return cy.wrap(resp.body);
    });
});
