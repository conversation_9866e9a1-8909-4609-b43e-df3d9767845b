import React from 'react';
import '@testing-library/jest-dom';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { TreezThemeProvider } from '@treez-inc/component-library';
import { RemoveVariantsConfirmModal } from '.';

describe('RemoveVariantsConfirmModal', () => {
    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    it('renders modal with correct title', () => {
        render(
            <TreezThemeProvider>
                <RemoveVariantsConfirmModal
                    allProducts={[]}
                    removeVariants={jest.fn()}
                    toggleRemoveVariantsConfirmModal={jest.fn()}
                    removeVariantsConfirmModalOpen
                    collectionName="Test Collection"
                    selectionModel={[]}
                    selectedProductsCount={0}
                    isRemoveButtonDisabled={false}
                />
            </TreezThemeProvider>
        );
        
        expect(screen.getByTestId('modal-content-remove-all-products')).toBeInTheDocument();
    });

    it('calls removeVariants when confirm button is clicked', async () => {
        const removeVariantsMock = jest.fn();
        const toggleRemoveVariantsConfirmModalMock = jest.fn();

        const { getByTestId } = render(
            <TreezThemeProvider>
                <RemoveVariantsConfirmModal
                    allProducts={[]}
                    removeVariants={removeVariantsMock}
                    toggleRemoveVariantsConfirmModal={toggleRemoveVariantsConfirmModalMock}
                    removeVariantsConfirmModalOpen
                    collectionName="Test Collection"
                    selectionModel={[]}
                    selectedProductsCount={0}
                    isRemoveButtonDisabled={false}
                />
            </TreezThemeProvider>
        );

        fireEvent.click(getByTestId('confirm-remove-variant-button'));
        await waitFor(() => {
            expect(removeVariantsMock).toHaveBeenCalled();
            expect(toggleRemoveVariantsConfirmModalMock).toHaveBeenCalled();
        });
    });

    it('closes remove variants modal when cancel button is clicked', () => {
        const toggleRemoveVariantsConfirmModalMock = jest.fn();

        const { getByTestId } = render(
            <TreezThemeProvider>
                <RemoveVariantsConfirmModal
                    allProducts={[]}
                    removeVariants={jest.fn()}
                    toggleRemoveVariantsConfirmModal={toggleRemoveVariantsConfirmModalMock}
                    removeVariantsConfirmModalOpen
                    collectionName="Test Collection"
                    selectionModel={[]}
                    selectedProductsCount={0}
                    isRemoveButtonDisabled={false}
                />
            </TreezThemeProvider>
        );

        fireEvent.click(getByTestId('cancel-remove-variant-button'));
        expect(toggleRemoveVariantsConfirmModalMock).toHaveBeenCalled();
    });
});
