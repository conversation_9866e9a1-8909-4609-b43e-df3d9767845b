import React from 'react';
import '@testing-library/jest-dom';
import {render} from '@testing-library/react';
import {TreezThemeProvider} from '@treez-inc/component-library';
import CenteredContent, {CenteredContentProps} from '.';

const testIdDefault = 'centered-content';

const defaultProps = {
    children: <div />,
};

describe('<CenteredContent />', () => {
    const renderCenteredContentComponent = (props: Partial<CenteredContentProps> = {}) => {
        const mergedProps = {...defaultProps, ...props};
        const {getByTestId} = render(
            <TreezThemeProvider>
                <CenteredContent {...mergedProps} />
            </TreezThemeProvider>
        );
        const contentContainer = getByTestId(mergedProps.testId ?? testIdDefault);
        return {contentContainer, getByTestId};
    };

    it('renders component with correct styles', () => {
        const {contentContainer} = renderCenteredContentComponent();

        expect(contentContainer).toHaveStyle('display: flex');
        expect(contentContainer).toHaveStyle('justifyContent: center');
        expect(contentContainer).toHaveStyle('alignItems: center');
        expect(contentContainer).toHaveStyle('height: 100%');
    });

    it('renders children', () => {
        const children = <div data-testid="children" />;

        const {getByTestId} = renderCenteredContentComponent({children});

        expect(getByTestId('children')).toBeInTheDocument();
    });

    it('renders component with default test id', () => {
        const {getByTestId} = renderCenteredContentComponent();

        expect(getByTestId(testIdDefault)).toBeInTheDocument();
    });

    it('default test id can be overridden', () => {
        const testId = 'overridden';
        const {getByTestId} = renderCenteredContentComponent({testId});

        expect(getByTestId(testId)).toBeInTheDocument();
    });
});
