import {basePath} from '../support/constants';
import {productCollectionApiUrl} from '../support/e2e';
import {productCollectionListData} from '../../src/test/testData';

const collectionListPageLayout = '[data-testid="collections-page-layout"]';
const collectionsListDataGrid = '[data-testid="collections-grid-layout"]';
const collectionsHeaderBreadcrumbs = '[data-testid="collection-breadcrumbs"]';
const collectionsTitle = '[data-testid="collections-header"]';
const collectionAddButton = '[data-testid="add-product-collection-button"]';

const collectionDeleteButton = '[data-testid="collection-action-delete"]';
const collectionMenuIcon = '[data-testid="collections-flyout-menu-more"]';
const errorPermissions = '[data-testid="error-permissions"]';
const errorNotFound = '[data-testid="error-not-found"]';
const errorDefault = '[data-testid="error-default"]';

describe('Collection View', () => {
    beforeEach(() => {
        cy.loginAs('admin');
    });

    it('renders collection view', () => {
        // setup
        cy.intercept('GET', `${productCollectionApiUrl}`, (req) => {
            req.reply({
                statusCode: 200,
                body: productCollectionListData,
            });
        });

        // exercise
        cy.visit(`${basePath}/`);

        // verify
        cy.get(collectionListPageLayout).should('be.visible');
        cy.get(collectionsListDataGrid).should('be.visible');
        cy.get(collectionsHeaderBreadcrumbs).should('be.visible');
        cy.get(collectionsTitle).should('be.visible');
        cy.get(collectionAddButton).should('be.visible');
        cy.get(collectionsListDataGrid).contains('Collection');
        cy.get(collectionsListDataGrid).contains('Last update');
        cy.get(collectionsListDataGrid).contains('Status');
    });

    it('pops up menu items when clicked on more button', () => {
        // setup
        cy.intercept('GET', `${productCollectionApiUrl}`, (req) => {
            req.reply({
                statusCode: 200,
                body: productCollectionListData,
            });
        });

        // exercise
        cy.visit(`${basePath}/`);

        cy.get(collectionMenuIcon).first().should('be.visible').click({force: true});
        // verify
        cy.get(collectionDeleteButton).should('be.visible');
    });

    it('renders permissions error', () => {
        // setup
        cy.intercept('GET', `${productCollectionApiUrl}`, (req) => {
            req.reply({statusCode: 403});
        });

        // exercise
        cy.visit(`${basePath}/`);

        // verify
        cy.get(collectionListPageLayout).should('not.exist');
        cy.get(collectionsListDataGrid).should('not.exist');
        cy.get(errorPermissions).should('be.visible');
    });

    it('renders not found error', () => {
        // setup
        cy.intercept('GET', `${productCollectionApiUrl}`, (req) => {
            req.reply({statusCode: 404});
        });

        // exercise
        cy.visit(`${basePath}/`);

        // verify
        cy.get(collectionListPageLayout).should('not.exist');
        cy.get(collectionsListDataGrid).should('not.exist');
        cy.get(errorNotFound).should('be.visible');
    });

    it('renders default error', () => {
        // setup
        cy.intercept('GET', `${productCollectionApiUrl}`, (req) => {
            req.reply({statusCode: 500});
        });

        // exercise
        cy.visit(`${basePath}/`);

        // verify
        cy.get(collectionListPageLayout).should('not.exist');
        cy.get(collectionsListDataGrid).should('not.exist');
        cy.get(errorDefault).should('be.visible');
    });
});
