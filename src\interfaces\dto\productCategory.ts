export interface UomProps {
    label: string;
    value: string;
}

export enum InputType {
    BOOLEAN = 'boolean',
    NUMBER = 'number',
    STRING = 'string',
    SELECT_OPTIONS = 'select',
}

export interface FormDetailsProps {
    label: string;
    input: string;
    inputType: InputType;
    required: boolean;
    toolTip?: string;
    icon?: string;
    options?: {
        displayValue: string | number;
        displayName: string;
    }[];
}

export interface ProductCategoryDto {
    id: string;
    name: string;
    isCannabis: boolean;
    uoms?: UomProps[];
    productFormDetails?: FormDetailsProps[];
    variantFormDetails?: FormDetailsProps[];
}
