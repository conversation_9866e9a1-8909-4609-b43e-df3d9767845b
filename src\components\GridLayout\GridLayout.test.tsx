import React from 'react';
import {render} from '@testing-library/react';
import '@testing-library/jest-dom';
import {convertPxToRem, TreezThemeProvider} from '@treez-inc/component-library';
import {allColors} from '@treez-inc/component-library/dist/components/TreezThemeProvider/treez-colors';
import GridLayout, {GridLayoutProps} from '.';

const defaultProps = {
    children: <div />,
    testId: 'testId',
};

describe('<GridLayout />', () => {
    const renderGridLayoutComponent = (props: Partial<GridLayoutProps> = {}) => {
        const mergedProps = {...defaultProps, ...props};
        const {getByTestId} = render(
            <TreezThemeProvider>
                <GridLayout {...mergedProps} />
            </TreezThemeProvider>
        );
        const gridLayout = getByTestId(mergedProps.testId);
        return {gridLayout, getByTestId};
    };

    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    it('should render with correct styles', () => {
        const {gridLayout} = renderGridLayoutComponent();

        expect(gridLayout).toBeInTheDocument();
        expect(gridLayout).toHaveStyle('height: 100%');
        expect(gridLayout).toHaveStyle(`background: ${allColors.primaryWhite.main}`);
        expect(gridLayout).toHaveStyle(`padding-right: ${convertPxToRem(52)}`);
        expect(gridLayout).toHaveStyle(`padding-left: ${convertPxToRem(52)}`);
    });

    it('should render children', () => {
        const children = <div data-testid="children" />;

        const {getByTestId} = renderGridLayoutComponent({children});

        expect(getByTestId('children')).toBeInTheDocument();
    });

    it('should render with the testId', () => {
        const testId = 'random-test-id';

        const {getByTestId} = renderGridLayoutComponent({testId});

        expect(getByTestId(testId)).toBeInTheDocument();
    });
});
