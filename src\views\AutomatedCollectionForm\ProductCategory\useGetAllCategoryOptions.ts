import { useQuery } from 'react-query';
import { UseQueryOptions } from 'react-query/types/react/types';
import { ProductCategoryDto } from '../../../interfaces/dto/productCategory';
import Entities from '../../../interfaces/entities.enum';
import { getData } from '../../../api/genericAccessor';
import { PRODUCT_CONTROL_API_URL } from '../../../constants/apiEndPoints';

const useGetAllCategoryOptions = (options?: UseQueryOptions): { data: ProductCategoryDto[]; isLoading: boolean } => {
    const { data, isLoading } = useQuery({
        queryKey: ['product-category'],
        queryFn: async () => {
            const result: ProductCategoryDto[] = await getData(PRODUCT_CONTROL_API_URL, Entities.PRODUCT_CATEGORY, {});
            return result;
        },
        ...options,
    });
    return { data: data as ProductCategoryDto[], isLoading };
};

export default useGetAllCategoryOptions;
