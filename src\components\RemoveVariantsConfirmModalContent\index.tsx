import React from 'react';
import { Typography } from '@mui/material/';
import { GridRowSelectionModel } from '@mui/x-data-grid-pro';
import { IProduct } from '../../interfaces/product';
import { hasVariantSelected } from '../../utils/filterUtil';

interface RemoveVariantsConfirmModalContentProps {
    collectionName: string;
    selectionModel: GridRowSelectionModel;
    modifiedData: IProduct[];
    selectedProductsCount: number;
}

const RemoveVariantsConfirmModalContent: React.FC<RemoveVariantsConfirmModalContentProps> = ({
    collectionName,
    selectionModel,
    modifiedData,
    selectedProductsCount,
}) => {
    const isRemoveAllProducts = selectionModel?.length === modifiedData?.length;

    let content;
    if (isRemoveAllProducts) {
        content = (
            <span data-testid="modal-content-remove-all-products">
                You are removing all products from <strong>{collectionName}</strong> collection.
                They will no longer be part of the mentioned collection, leaving it empty.
            </span>
        );
    } else if (selectedProductsCount > 1 && hasVariantSelected(selectionModel, modifiedData)) {
        content = (
            <span data-testid="modal-content-remove-some-products">
                You are removing {selectedProductsCount} product
                {selectedProductsCount > 1 ? 's' : ''} and selected variants from the{' '}
                <strong>{collectionName}</strong> collection. They will no longer be part of the
                mentioned collection.
            </span>
        );
    } else if (selectedProductsCount === 1) {
        content = (
            <span data-testid="modal-content-remove-one-product">
                You are removing the selected Variants from the <strong>{collectionName}</strong> collection.
                They will no longer be part of the mentioned collection.
            </span>
        );
    } else {
        content = (
            <span data-testid="modal-content-remove-products">
                You are removing {selectedProductsCount} products from the{' '}
                <strong>{collectionName}</strong> collection. They will no longer be part of the
                mentioned collection.
            </span>
        );
    }

    return <Typography variant="body2">{content}</Typography>;
};

export { RemoveVariantsConfirmModalContent };
