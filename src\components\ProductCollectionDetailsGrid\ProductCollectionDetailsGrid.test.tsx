import React from 'react';
import {render, screen} from '@testing-library/react';
import '@testing-library/jest-dom';
import {TreezThemeProvider} from '@treez-inc/component-library';
import {MemoryRouter} from 'react-router-dom';
import {ProductCollectionDetailsGrid, transformProductData} from '.';
import {productListData} from '../../test/testData';
import {IProduct} from '../../interfaces/product';

jest.mock('../../hooks/usePermissionsContext');
jest.mock('../../hooks/useApiContext');
jest.mock('../../hooks/useSnackbarContext');

const mockUserPermissionsContext = require('../../hooks/usePermissionsContext').default;
const mockUseApiContext = require('../../hooks/useApiContext').default;
const mockUseSnackbarContext = require('../../hooks/useSnackbarContext').default;

const mockData: IProduct[] = transformProductData(productListData.data);
const orgId = '4a873b6d-e350-4955-a254-bb37b8bc81f6';
const mockApi = jest.fn();
const mockSetSnackbar = jest.fn();
const setIsLoading = jest.fn();
const refetch = jest.fn();
const permission = {
    permissions: {},
    orgId,
};
describe('<ProductCollectionDetailsGrid />', () => {
    beforeEach(() => {
        mockUseApiContext.mockImplementation(() => mockApi);
        mockUserPermissionsContext.mockImplementation(() => permission);
        mockUseSnackbarContext.mockReturnValue({
            setSnackbar: mockSetSnackbar,
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    it('renders ProductCollectionDetailsGrid with data', () => {
        render(
            <MemoryRouter>
                <TreezThemeProvider>
                    <ProductCollectionDetailsGrid
                        data={mockData}
                        refetch={refetch}
                        loading={false}
                        collectionId="e1cac016-0877-4e93-8532-334dadeb9daa"
                        collectionName="Collection A"
                        setIsLoading={setIsLoading}
                    />
                </TreezThemeProvider>
            </MemoryRouter>
        );

        expect(screen.getByRole('grid')).toBeInTheDocument();
        expect(screen.getByText('Product')).toBeInTheDocument();
    });

    it('renders ProductCollectionDetailsGrid without data', () => {
        render(
            <MemoryRouter>
                <TreezThemeProvider>
                    <ProductCollectionDetailsGrid
                        data={[]}
                        refetch={refetch}
                        loading={false}
                        collectionId="00000-0000-0000-00000"
                        collectionName="test collection"
                        setIsLoading={setIsLoading}
                    />
                </TreezThemeProvider>
            </MemoryRouter>
        );

        expect(screen.getByRole('grid')).toBeInTheDocument();
        expect(screen.getByText('Product')).toBeInTheDocument();
        expect(screen.getByText('This collection is empty')).toBeInTheDocument();
    });

    it('renders ProductCollectionDetailsGrid with loading state', () => {
        render(
            <MemoryRouter>
                <TreezThemeProvider>
                    <ProductCollectionDetailsGrid
                        data={mockData}
                        refetch={refetch}
                        loading
                        collectionId="00000-0000-0000-00000"
                        collectionName="test collection"
                        setIsLoading={setIsLoading}
                    />
                </TreezThemeProvider>
            </MemoryRouter>
        );

        expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('renders ProductCollectionDetailsGrid with add new product to collection link', () => {
        render(
            <MemoryRouter>
                <TreezThemeProvider>
                    <ProductCollectionDetailsGrid
                        data={mockData}
                        refetch={refetch}
                        loading
                        collectionId="00000-0000-0000-00000"
                        collectionName="test collection"
                        setIsLoading={setIsLoading}
                    />
                </TreezThemeProvider>
            </MemoryRouter>
        );

        expect(screen.getByTestId('add-new-product-to-collection-link')).toBeInTheDocument();
    });
});
