import React from 'react';
import { MemoryRouter } from 'react-router-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { TreezThemeProvider } from '@treez-inc/component-library';
import { QueryClient, QueryClientProvider } from 'react-query';
import AutomatedCollectionForm from '.';
import ApiService from '../../utils/apiService';
import SnackbarProvider from '../../providers/SnackbarProvider';
import '@testing-library/jest-dom';


jest.mock('../../hooks/useProductCollections');
jest.mock('../../hooks/useApiContext');
jest.mock('../../hooks/usePermissionsContext');
jest.mock('../../hooks/useSnackbarContext');

jest.mock('./ProductCategory/useGetAllCategoryOptions');
jest.mock('../../hooks/useGetProductCollectionDetails');
jest.mock('../../hooks/useGetAttributeCategoriesOptions');
jest.mock('../../hooks/useGetProductSubCategoriesOptions');
jest.mock('../../hooks/useGetProductBrandsOptions');
jest.mock('../../queries/useProductSearchQuery');

const queryClientProvider = new QueryClient();
const mockedUseNavigate = jest.fn();
const mockSetSnackbar = jest.fn();

jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useParams: () => ({ id: '123' }),
    useNavigate: () => mockedUseNavigate,
}));

jest.mock('../../api/genericAccessor', () => ({
    getAuthTokens: jest.fn().mockReturnValue({
        accessToken: 'dummyAccessToken',
        refreshToken: 'dummyRefreshToken',
    }),
    createData: jest.fn(),
}));

const mockUseApiContext = require('../../hooks/useApiContext').default;
const mockAddAutomatedProductCollectionModal = require('../../hooks/useProductCollections').default;
const mockUseSnackbarContext = require('../../hooks/useSnackbarContext').default;

const mockGetAllCategoryOptions = require('./ProductCategory/useGetAllCategoryOptions').default;
const mockGetProductCollectionDetails = require('../../hooks/useGetProductCollectionDetails').default;
const mockGetAttributeCategoriesOptions = require('../../hooks/useGetAttributeCategoriesOptions').default;
const mockGetProductSubCategoriesOptions = require('../../hooks/useGetProductSubCategoriesOptions').default;
const mockGetProductBrandsOptions = require('../../hooks/useGetProductBrandsOptions').default;
const mockProductSearchQuery = require('../../queries/useProductSearchQuery').default;

const validTokens = {
    accessToken: 'accessToken',
    expiresIn: 300,
    refreshToken: 'refreshToken',
    idToken: 'idToken',
};

const clearTokens = jest.fn();
const getTokens = () => validTokens;
const redirectToLogin = jest.fn();
const apiService = new ApiService(getTokens, clearTokens, redirectToLogin);
const spyApiPost = jest.spyOn(apiService.api, 'post');

const dummyOrgId = '00000-00000-00000-00000-00000-00000';
const dummyCategoryId = '00000-11111-22222-33333-44444-55555';
const dummySubCategoryId = '11111-22222-33333-44444-55555-66666';
const dummyBrandId = '22222-33333-44444-55555-66666-77777';
const dummyCategoryAttributeId = '33333-44444-55555-66666-77777-88888';
const dummyProductCollectionId = '44444-55555-66666-77777-88888-99999';
const dummyProductId = '55555-66666-77777-88888-99999-11111';
const dummyVariantId = '66666-77777-88888-99999-11111-22222';

const apiResponse = {
    data: {
        id: 'd393a44a-ecfe-4084-9554-a20bc59d7d59',
        name: 'dummyCollection',
        organizationId: dummyOrgId,
        sync: false,
        createdAt: '2023-11-20T08:00:50.864Z',
        updatedAt: '2023-11-20T08:00:50.864Z',
        deletedAt: '',
    },
};

const productCollectionData = [
    {
        id: 'd393a44a-ecfe-4084-9554-a20bc59d7d59',
        name: 'dummyCollectionA',
        sync: true,
        createdAt: '2023-11-20T08:00:50.864Z',
        updatedAt: '2023-11-20T08:00:50.864Z',
        deletedAt: null,
    },
    {
        id: 'd393a44a-ecfe-4084-9554-a20bc59d7d58',
        name: 'dummyCollectionB',
        sync: true,
        createdAt: '2023-11-20T08:00:50.864Z',
        updatedAt: '2023-11-20T08:00:50.864Z',
        deletedAt: null,
    },
    {
        id: 'd393a44a-ecfe-4084-9554-a20bc59d7d58',
        name: 'TesingMaxLengthNameForCollectionNameInTheModalAddCollectionProductCollection',
        sync: true,
        createdAt: '2023-11-20T08:00:50.864Z',
        updatedAt: '2023-11-20T08:00:50.864Z',
        deletedAt: null,
    },
];

const productCategoriesData = [
    {
        "id": dummyCategoryId,
        "createdAt": "2023-08-11T05:51:00.161Z",
        "updatedAt": "2023-08-11T05:51:00.161Z",
        "deletedAt": null,
        "name": "Test Category",
        "isCannabis": true,
        "uoms": [
            {
                "label": "GRAMS",
                "value": "GRAMS"
            }
        ],
        "productFormDetails": [],
        "variantFormDetails": []
    }
];

const productCategoriesAttributesData = [
    {
        "id": dummyCategoryAttributeId,
        "createdAt": "2023-10-19T09:26:04.198Z",
        "updatedAt": "2023-10-19T09:26:04.198Z",
        "deletedAt": null,
        "name": "Aroma",
        "organizationId": dummyOrgId,
        "verifiedReferenceId": null,
        "attributes": [
            {
                "id": dummyCategoryAttributeId,
                "createdAt": "2023-10-19T09:32:36.922Z",
                "updatedAt": "2023-10-19T09:32:36.922Z",
                "deletedAt": null,
                "name": "dummy attribute",
                "attributeCategoryId": dummyCategoryId,
                "organizationId": dummyOrgId,
                "aliasToId": null,
                "isRetired": false,
                "verifiedReferenceId": null
            }
        ]
    }
];

const productSubCategoriesData = [
    {
        "id": dummySubCategoryId,
        "createdAt": "2023-06-30T21:53:53.759Z",
        "updatedAt": "2023-06-30T21:53:53.759Z",
        "deletedAt": null,
        "name": "Dummy Plant",
        "isCannabis": true,
        "productCategoryId": dummyCategoryId
    }
];

const productBrandsData = [
    {
        "id": dummyBrandId,
        "createdAt": "2023-10-10T06:18:58.895Z",
        "updatedAt": "2023-10-10T06:18:58.895Z",
        "deletedAt": null,
        "name": "Dummy Brand",
        "label": null,
        "description": null,
        "organizationId": dummyOrgId,
        "verifiedReferenceId": null
    }
];

const productCollectionDetailsData = {
    "id": dummyProductCollectionId,
    "name": "dummy collection",
    "organizationId": dummyOrgId,
    "sync": true,
    "rule": {
        "category": [
            dummyCategoryId
        ]
    },
    "createdAt": "2024-10-14T06:27:01.236Z",
    "updatedAt": "2024-11-14T13:55:19.707Z",
    "deletedAt": null,
    "collectionItems": [
        {
            "id": "b8bcf0b8-d260-415b-b07b-cc5f996070b8",
            "collectionId": dummyProductCollectionId,
            "variantId": "4283fda2-a01a-49e5-a35c-5404339f5391",
            "sampleId": null,
            "promoId": null,
            "displayId": null,
            "isExclude": false,
            "createdAt": "2024-10-24T04:04:44.164Z",
            "updatedAt": "2024-11-14T13:55:19.707Z",
            "deletedAt": null
        },
        {
            "id": "8b69d8ac-3516-4f01-9eed-8a3108aa85bc",
            "collectionId": dummyProductCollectionId,
            "variantId": "7032dcdd-8863-46e7-8e4d-ada35d07165d",
            "sampleId": null,
            "promoId": null,
            "displayId": null,
            "isExclude": false,
            "createdAt": "2024-10-24T04:04:44.164Z",
            "updatedAt": "2024-11-14T13:55:19.707Z",
            "deletedAt": null
        }
    ]
};

const mockProductsData = {
    "data": [
        {
            "productCategoryId": dummyCategoryId,
            "productCategoryName": "Dummy Category",
            "productSubCategoryId": dummySubCategoryId,
            "productSubCategoryName": "Dummy SubCategory",
            "brandName": "B2B",
            "productId": dummyProductId,
            "productName": "Dummy Product",
            "brandId": dummyBrandId,
            "classification": null,
            "strain": null,
            "status": "active",
            "lastUpdated": "2024-10-30T16:56:34.992Z",
            "organizationId": dummyOrgId,
            "variants": [
                {
                    "id": dummyVariantId,
                    "name": "Dummy Variant",
                    "defaultPrices": {
                        "base": 600
                    },
                    "status": "active",
                    "sku": null,
                    "additionalSku": [],
                    "details": {
                        "isPromo": false,
                        "isSample": false,
                        "totalMgThc": 1,
                        "hideFromEcomMenu": false
                    },
                    "amount": 1,
                    "uom": "milligrams",
                    "unitCount": 1,
                    "merchandiseSize": null,
                    "promo": null,
                    "sample": null,
                    "updatedAt": "2024-11-21T09:36:16.256071+00:00",
                    "productId": dummyProductId,
                    "label": "1 mg"
                }
            ],
            "allSizes": "1",
        }
    ],
    "totalRecords": 1,
    "page": 0,
    "pageSize": 1
}

describe('AutomatedCollectionForm component', () => {
    beforeEach(() => {
        mockUseApiContext.mockImplementation(() => apiService);
        spyApiPost.mockResolvedValue(apiResponse);
        mockUseSnackbarContext.mockReturnValue({
            setSnackbar: mockSetSnackbar,
        });

        mockGetAllCategoryOptions.mockReturnValue({
            isLoading: false,
            error: null,
            data: productCategoriesData,
        });

        mockGetProductCollectionDetails.mockReturnValue({
            isLoading: false,
            error: null,
            data: productCollectionDetailsData,
        });

        mockGetAttributeCategoriesOptions.mockReturnValue({
            isLoading: false,
            error: null,
            data: productCategoriesAttributesData,
        });

        mockGetProductSubCategoriesOptions.mockReturnValue({
            isLoading: false,
            error: null,
            data: productSubCategoriesData,
        });

        mockGetProductBrandsOptions.mockReturnValue({
            isLoading: false,
            error: null,
            data: productBrandsData,
        });

        mockProductSearchQuery.mockReturnValue({
            isLoading: false,
            error: null,
            data: mockProductsData,
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.restoreAllMocks();
    });

    const renderFormComponent = () =>
        render(
            <QueryClientProvider client={queryClientProvider}>
                <MemoryRouter>
                <TreezThemeProvider>
                    <SnackbarProvider>
                        <AutomatedCollectionForm />
                    </SnackbarProvider>
                </TreezThemeProvider>
                </MemoryRouter>
            </QueryClientProvider>
        );

    const renderFormComponentWithField = () => {
        const { getByTestId } = renderFormComponent();
        const nameField = getByTestId('automated-collection-name-field');
        const nameInput = nameField.querySelector('input');
        const getNameHelperText = () => nameField.querySelector('p');
        const primaryButton = getByTestId('create-collection-form-button');
        const secondaryButton = getByTestId('cancel-collection-form-button');
        const title =  getByTestId('auto-collection-title');
        return {
            nameField,
            nameInput,
            getNameHelperText,
            primaryButton,
            secondaryButton,
            title
        };
    };

    it('Should render the components', () => {
        mockAddAutomatedProductCollectionModal.mockReturnValue({
            loading: false,
            error: null,
            data: { productCollectionData: [{}] },
        });
        const { nameInput, primaryButton } = renderFormComponentWithField();
        expect(nameInput).toBeInTheDocument();
        expect(primaryButton).toBeInTheDocument();
    });

    it('Renders RouteError when ProductCollection returns an error', () => {
        const mockError = new Error('Data fetching error');
        mockAddAutomatedProductCollectionModal.mockReturnValue({
            loading: false,
            error: mockError,
            data: null,
            status: 404,
        });

        renderFormComponent();
        const errorfield = screen.getByTestId('error-template');
        expect(errorfield).toBeInTheDocument();
        expect(screen.queryByTestId('collection-details-page-layout')).not.toBeInTheDocument();
    });

    it('Should show error message on entering existing collection name', async () => {
        mockAddAutomatedProductCollectionModal.mockReturnValue({
            loading: false,
            error: null,
            data: { data: productCollectionData },
        });
        const { nameInput, getNameHelperText, primaryButton } = renderFormComponentWithField();

        if (nameInput) {
            fireEvent.change(nameInput, { target: { value: 'dummyCollectionB' } });
            fireEvent.click(primaryButton);
        }

        await waitFor(() => {
            expect(getNameHelperText()).toBeInTheDocument();
            expect(primaryButton).toHaveAttribute('disabled');
            expect.any(Error);
        });
    });

    it('Should show error message on entering maximum length collection name', async () => {
        mockAddAutomatedProductCollectionModal.mockReturnValue({
            loading: false,
            error: null,
            data: { data: productCollectionData },
        });
    
        const {
            nameInput,
            getNameHelperText,
            primaryButton,
        } = renderFormComponentWithField();
    
        const longName = 'MaxLengthNameForCollectionNameInTheModalAddCollectionProductCollection';
    
        if (nameInput) {
            fireEvent.change(nameInput, {
                target: { value: longName },
            });
        }
    
        fireEvent.click(primaryButton);
    
        await waitFor(() => {
            expect(getNameHelperText()).toBeInTheDocument();
            expect(primaryButton).toHaveAttribute('disabled');
            expect.any(Error);
        });
    });

    it('Should not show error message on allowed length collection name', async () => {
        mockAddAutomatedProductCollectionModal.mockReturnValue({
            loading: false,
            error: null,
            data: { data: productCollectionData },
        });
        const { nameInput, getNameHelperText, primaryButton } = renderFormComponentWithField();

        if (nameInput) {
            fireEvent.change(nameInput, {
                target: { value: 'MaxLengthNameForCollectionNameInTheModalAddCollection' },
            });
        }

        await waitFor(() => {
            expect(getNameHelperText()).not.toBeInTheDocument();
            expect(primaryButton).toBeInTheDocument();
        });
    });

    it('Should show error message on entering maximum length existing collection name', async () => {
        mockAddAutomatedProductCollectionModal.mockReturnValue({
            loading: false,
            error: null,
            data: { data: productCollectionData },
        });
        const { nameInput, getNameHelperText, primaryButton } = renderFormComponentWithField();

        if (nameInput) {
            fireEvent.change(nameInput, {
                target: {
                    value: 'TesingMaxLengthNameForCollectionNameInTheModalAddCollectionProductCollection',
                },
            });
        }

        fireEvent.click(primaryButton);
    
        await waitFor(() => {
            expect(getNameHelperText()).toBeInTheDocument();
            expect(primaryButton).toHaveAttribute('disabled');
            expect.any(Error);
        });
    });

    it('Should be able to Create Collection Successfully', async () => {
        mockAddAutomatedProductCollectionModal.mockReturnValue({
            loading: false,
            error: null,
            data: { data: productCollectionData },
        });
        const { nameInput, primaryButton, secondaryButton } = renderFormComponentWithField();

        if (nameInput) {
            fireEvent.change(nameInput, { target: { value: 'dummyCollection Z' } });
            fireEvent.click(primaryButton);
        }

        await waitFor(() => {
            expect(primaryButton).toBeInTheDocument();
            expect(secondaryButton).toBeInTheDocument();
        });
    });

    it('Title should say Edit Automated Collection when in edit mode', async () => {
        const {
            title
        } = renderFormComponentWithField();

        expect(title.innerHTML).toContain("Edit Automated Collection");
    })

    it('Should be able to give collection name and cancel the collection create', async () => {
        mockAddAutomatedProductCollectionModal.mockReturnValue({
            loading: false,
            error: null,
            data: { data: productCollectionData },
        });
        const { nameInput, primaryButton, secondaryButton } = renderFormComponentWithField();

        if (nameInput) {
            fireEvent.change(nameInput, { target: { value: 'dummyCollection Z' } });
            fireEvent.click(secondaryButton);
        }

        await waitFor(() => {
            expect(primaryButton).toBeInTheDocument();
            expect(secondaryButton).toBeInTheDocument();
        });
    });
});
