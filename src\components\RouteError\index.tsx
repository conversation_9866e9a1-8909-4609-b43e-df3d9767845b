import React from 'react';
import {AxiosError} from 'axios';
import ErrorNotFound from '../ErrorNotFound';
import ErrorDefault from '../ErrorDefault';
import ErrorPermissions from '../ErrorPermissions';

const RouteError: React.FC<{error: unknown}> = ({error}) => {
    if (error instanceof AxiosError) {
        const {response} = error;
        if (response?.status === 404) {
            return <ErrorNotFound />;
        }

        if (response?.status === 403) {
            return <ErrorPermissions />;
        }
    }

    return <ErrorDefault />;
};

export default RouteError;
