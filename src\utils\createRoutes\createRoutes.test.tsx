import React from 'react';
import {basePath} from '../../constants/routes';
import createRoutes from '.';
import ProductCollection from '../../views/ProductCollection';
import {MFEPermissions} from '../../interfaces/permissions';
import PermissionedRoute from '../../components/PermissionedRoute';
import ErrorNotFound from '../../components/ErrorNotFound';
import ErrorDefault from '../../components/ErrorDefault';
import ProductCollectionDetails from '../../views/ProductCollectionDetails';
import AutomatedCollectionForm from '../../views/AutomatedCollectionForm';

describe('Create Routes', () => {
    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    it('should create the correct routes configuration', () => {
        const actual = createRoutes();

        expect(actual).toEqual([
            {
                path: basePath,
                errorElement: <ErrorDefault />,
                children: [
                    {
                        index: true,
                        element: (
                            <PermissionedRoute
                                anyOf={[
                                    MFEPermissions.VIEW_PRODUCT_COLLECTIONS,
                                    MFEPermissions.MANAGE_PRODUCT_COLLECTIONS,
                                ]}
                            >
                                <ProductCollection />
                            </PermissionedRoute>
                        ),
                    },
                    {
                        path: `${basePath}/:id`,
                        element: (
                            <PermissionedRoute
                                anyOf={[
                                    MFEPermissions.VIEW_PRODUCT_COLLECTIONS,
                                    MFEPermissions.MANAGE_PRODUCT_COLLECTIONS,
                                ]}
                            >
                                <ProductCollectionDetails />
                            </PermissionedRoute>
                        ),
                    },
                    {
                        path: `${basePath}/automated/add`,
                        element: (
                            <PermissionedRoute
                                anyOf={[
                                    MFEPermissions.VIEW_PRODUCT_COLLECTIONS,
                                    MFEPermissions.MANAGE_PRODUCT_COLLECTIONS,
                                ]}
                            >
                                <AutomatedCollectionForm />
                            </PermissionedRoute>
                        ),
                    },
                    {
                        path: `${basePath}/automated/:id`,
                        element: (
                            <PermissionedRoute
                                anyOf={[
                                    MFEPermissions.VIEW_PRODUCT_COLLECTIONS,
                                    MFEPermissions.MANAGE_PRODUCT_COLLECTIONS,
                                ]}
                            >
                                <AutomatedCollectionForm />
                            </PermissionedRoute>
                        ),
                    },
                    {
                        path: '*',
                        element: <ErrorNotFound />,
                    },
                ],
            },
            {
                path: '*',
                element: <ErrorNotFound />,
            },
        ]);
    });
});
