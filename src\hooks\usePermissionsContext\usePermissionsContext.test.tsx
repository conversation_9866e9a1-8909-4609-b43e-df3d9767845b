import {renderHook, waitFor} from '@testing-library/react';
import React from 'react';
import usePermissionsContext from '.';
import PermissionsProvider from '../../providers/PermissionsProvider';

describe('useUserPermissionsContext()', () => {
    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });
    it('should throw expected error when called outside a permissions provider', async () => {
        const expectedError = new Error(
            'usePermissionsContext must be used within a PermissionsProvider'
        );

        await renderHook(() => {
            try {
                usePermissionsContext();
            } catch (e) {
                expect(e as Error).toEqual(expectedError);
            }
        });
    });

    it('should return user permission context when called within a permissions provider', async () => {
        const permission1 = 'permission1';
        const permission2 = 'permission2';
        const getPermissions = jest.fn();
        getPermissions.mockResolvedValue({
            permissions: [permission1, permission2],
            id: 'orgId',
        });

        const wrapper = ({children}: {children: React.ReactNode}) => (
            <PermissionsProvider getPermissions={getPermissions}>{children}</PermissionsProvider>
        );

        const {result} = await renderHook(() => usePermissionsContext(), {
            wrapper,
        });

        await waitFor(() => {
            expect(getPermissions).toHaveBeenCalledTimes(1);
            expect(result.current).toEqual({
                permissions: {[permission1]: true, [permission2]: true},
                orgId: 'orgId',
            });
        });
    });
});
