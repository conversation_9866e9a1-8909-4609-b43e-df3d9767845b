import { useMutation, useQ<PERSON>yClient } from 'react-query';
import { MutationKeyConfig, QueryKeyConfig } from '../../api/types';
import { createData, deleteData, updateData } from '../../api/genericAccessor';
import { COLLECTIONS_URL } from '../../constants/apiEndPoints';

interface CreateDataParams {
    mutationConfig: MutationKeyConfig;
    invalidateQueryKeys?: (data: any) => QueryKeyConfig;
}

interface MutationVariables {
    createData?: any;
    updateData?: any;
    deleteData?: { ids: any[] };
}

interface ApiResponse<T> {
    data?: T;
    error?: any;
}

export interface MutationResponse<T> {
    createData?: ApiResponse<T>;
    updateData?: ApiResponse<T>;
    deleteData?: ApiResponse<T>;
}

type RequestApi = (apiBaseUrl:string, entity: string, data: any) => Promise<any>;

const errorWrapper = async <T>(apiBaseUrl: string, request: RequestApi, route: string, data: any): Promise<ApiResponse<T>> => {
    try {
        const result = await request(apiBaseUrl, route, data);
        return { data: result.data ? result.data : result, error: result.failed };
    } catch (error) {
        return { error };
    }
};

const saveData = async <T>(apiBaseUrl: string, data: MutationVariables, route: string): Promise<MutationResponse<T>> => {
    const result = await Promise.all([
        data.createData ? errorWrapper<T>(apiBaseUrl, createData, route, data.createData) : Promise.resolve(undefined),
        data.updateData ? errorWrapper<T>(apiBaseUrl, updateData, route, data.updateData) : Promise.resolve(undefined),
        data.deleteData ? errorWrapper<T>(apiBaseUrl, deleteData, route, data.deleteData) : Promise.resolve(undefined),
    ]);

    return { createData: result[0], updateData: result[1], deleteData: result[2] };
};

const useSaveData = <T>({ mutationConfig, invalidateQueryKeys }: CreateDataParams) => {
    const client = useQueryClient();

    return useMutation<MutationResponse<T>, any, MutationVariables, any>({
        mutationKey: mutationConfig.mutationKey,
        mutationFn: (data: MutationVariables) => saveData(COLLECTIONS_URL, data, mutationConfig.route),
        onSuccess: ({ data }: any) => {
            if (invalidateQueryKeys) {
                client.invalidateQueries(invalidateQueryKeys(data).queryKey);
            }
        },
    });
};

export default useSaveData;
