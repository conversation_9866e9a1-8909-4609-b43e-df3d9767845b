import {
    areAllVariantsSelected,
    filterCollectionList,
    getAllProductsData,
    getProductByVariantIds,
    getSelectedProductsData,
    hasVariantSelected,
    removeSelectedProducts,
    removeVariantsAndExtractIds,
    selectedProductVariants,
} from '.';
import CollectionStatus from '../../interfaces/collectionStatus.enum';
import {productCollectionListData, productListData} from '../../test/testData';
import {transformProductData} from '../../components/ProductCollectionDetailsGrid';

describe('filterCollectionList', () => {
    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    const mockCollectionList = productCollectionListData.data;

    it('should return the original list when no filters are applied', () => {
        const filterObj = {
            status: [],
        };
        const result = filterCollectionList(mockCollectionList, filterObj);
        expect(result).toEqual(mockCollectionList);
    });

    it('should filter by ACTIVE status', () => {
        const filterObj = {
            status: [CollectionStatus.ACTIVE],
        };
        const result = filterCollectionList(mockCollectionList, filterObj);
        expect(result).toHaveLength(3);
        expect(result[0].id).toEqual('91787d3b-f7c1-4f99-a25e-b021ba899e9d');
    });

    it('should filter by INACTIVE status', () => {
        const filterObj = {
            status: [CollectionStatus.INACTIVE],
        };
        const result = filterCollectionList(mockCollectionList, filterObj);
        expect(result).toHaveLength(1);
        expect(result[0].id).toEqual('a376217e-3084-4610-a6fe-c3d192d34af6');
    });

    it('should return an empty list when filtering by an unknown status', () => {
        const filterObj = {
            status: ['UNKNOWN_STATUS'],
        };
        const result = filterCollectionList(mockCollectionList, filterObj);
        expect(result).toHaveLength(0);
    });

    it('should return all list when filtering by both ACTIVE and INACTIVE status', () => {
        const filterObj = {
            status: [CollectionStatus.ACTIVE, CollectionStatus.INACTIVE],
        };
        const result = filterCollectionList(mockCollectionList, filterObj);
        expect(result).toHaveLength(4);
    });
});

describe('removeVariantsAndExtractIds', () => {
    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    const mockproductList = productListData.data;

    it('should remove variants and extract variant Ids', () => {
        const variantIdsToRemove = [
            'eef22692-3a97-4b3f-89c2-08bba6dd3ce8',
            'f3c64d2f-a439-48dc-9042-90f045a0916a',
        ];

        const result = removeVariantsAndExtractIds(mockproductList, variantIdsToRemove);
        expect(result).toEqual([
            {variantId: '358c7c40-1cfb-4c19-8fda-a3f06e316c93'},
            {variantId: '23bc9e74-164f-4752-9f69-7a2aa0951bbc'},
        ]);
    });
});

describe('getAllProductsData', () => {
    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    const mockProductList = transformProductData(productListData.data);

    it('should return an empty array if productList is empty', () => {
        const result = getAllProductsData([]);
        expect(result).toEqual([]);
    });

    it('should return an array of productList is available', () => {
        const result = getAllProductsData(mockProductList);
        const expectedResult = mockProductList.filter((product) => product.isChild === false);
        expect(result).toEqual(expectedResult);
    });
});

describe('selectedProductVariants', () => {
    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    const mockProductList = transformProductData(productListData.data);

    it('should return an empty array if selectedIds is empty', () => {
        const result = selectedProductVariants([], mockProductList);
        expect(result).toEqual([]);
    });

    it('should return an empty array if productList is empty', () => {
        const selectedVariants = ['7c2ea386-923e-4801-aaaf-9a5d2d4e450a'];

        const result = selectedProductVariants(selectedVariants, []);
        expect(result).toEqual([]);
    });

    it('should return an array of selected child products', () => {
        const selectedVariants = [
            '7c2ea386-923e-4801-aaaf-9a5d2d4e450a',
            'eef22692-3a97-4b3f-89c2-08bba6dd3ce8',
        ];

        const result = selectedProductVariants(selectedVariants, mockProductList);
        expect(result).toEqual(['eef22692-3a97-4b3f-89c2-08bba6dd3ce8']);
    });
});

describe('getSelectedProductsData', () => {
    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    const mockProductList = transformProductData(productListData.data);

    it('should return an empty array if productList is empty', () => {
        const selectedVariants = [
            '7c2ea386-923e-4801-aaaf-9a5d2d4e450a',
            'eef22692-3a97-4b3f-89c2-08bba6dd3ce8',
        ];

        const result = getSelectedProductsData(selectedVariants, []);
        expect(result).toEqual([]);
    });

    it('should return array of products if selectedIds and products are available', () => {
        const selectedIds = [
            '7c2ea386-923e-4801-aaaf-9a5d2d4e450a',
            'eef22692-3a97-4b3f-89c2-08bba6dd3ce8',
        ];

        const result = getSelectedProductsData(selectedIds, mockProductList);
        const expectedResult = mockProductList
            .filter((product) => selectedIds.includes(product.id))
            .map((product) => ({id: product.id}));
        expect(result).toEqual(expectedResult);
    });
});

describe('areAllVariantsSelected', () => {
    const mockProductList = transformProductData(productListData.data);

    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    it('should return false when not all child products are selected for the given parentId', () => {
        const parentId = '83d519f1-dc34-4d49-a099-f8dd4d9bdaab';
        const selectedIds = ['358c7c40-1cfb-4c19-8fda-a3f06e316c93'];

        const result = areAllVariantsSelected(mockProductList, parentId, selectedIds);
        expect(result).toBe(false);
    });

    it('should return true when all child products are selected for the given parentId', () => {
        const parentId = '83d519f1-dc34-4d49-a099-f8dd4d9bdaab';
        const selectedIds = [
            '358c7c40-1cfb-4c19-8fda-a3f06e316c93',
            '23bc9e74-164f-4752-9f69-7a2aa0951bbc',
        ];

        const result = areAllVariantsSelected(mockProductList, parentId, selectedIds);
        expect(result).toBe(true);
    });
});

describe('removeSelectedProducts', () => {
    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    const mockProductList = transformProductData(productListData.data);

    it('should return the original array when selectedIds array is empty', () => {
        const selectedIds: string[] = [];

        const result = removeSelectedProducts(mockProductList, selectedIds);
        expect(result).toStrictEqual(mockProductList);
    });

    it('should remove selected parent product and its variants', async () => {
        const selectedIds: string[] = [
            '83d519f1-dc34-4d49-a099-f8dd4d9bdaab',
            '358c7c40-1cfb-4c19-8fda-a3f06e316c93',
            '23bc9e74-164f-4752-9f69-7a2aa0951bbc',
        ];

        const result = removeSelectedProducts(mockProductList, selectedIds);
        const expectedResult = [mockProductList[0], mockProductList[1], mockProductList[2]];

        expect(result).toStrictEqual(expectedResult);
    });
});

describe('hasVariantSelected', () => {
    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    const mockProductList = transformProductData(productListData.data);

    it('should return false when selectedIds array is empty', () => {
        const selectedIds: string[] = [];

        const result = hasVariantSelected(selectedIds, mockProductList);
        expect(result).toBe(false);
    });

    it('should return true when at least one variant is selected', () => {
        const selectedIds: string[] = ['358c7c40-1cfb-4c19-8fda-a3f06e316c93'];

        const result = hasVariantSelected(selectedIds, mockProductList);

        expect(result).toBe(true);
    });
});

describe('getProductByVariantIds', () => {
    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    const mockProductList = transformProductData(productListData.data);

    it('should return an empty array when selectedVariantIds array is empty', () => {
        const selectedVariantIds: string[] = [];

        const result = getProductByVariantIds(mockProductList, selectedVariantIds);
        expect(result).toEqual([]);
    });

    it('should return an array of productIds for matching variant ids', () => {
        const selectedVariantIds: string[] = ['358c7c40-1cfb-4c19-8fda-a3f06e316c93'];

        const result = getProductByVariantIds(mockProductList, selectedVariantIds);
        const expectedResult = mockProductList
            .filter(
                (product) =>
                    product.variants &&
                    product.variants.some(
                        (variant) => selectedVariantIds.includes(variant.id) && !product.isChild
                    )
            )
            .map((product) => product.productId);

        expect(result).toEqual(expectedResult);
    });
});
