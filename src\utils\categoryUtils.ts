import { IconName } from '@treez-inc/component-library/dist/components/Icon/types';
import PRODUCT_CATEGORIES from './product-categories-data';

const categoryMap = {
    [PRODUCT_CATEGORIES.Beverage]: 'Beverage',
    [PRODUCT_CATEGORIES.Cartridge]: 'Cartridge',
    [PRODUCT_CATEGORIES.Edible]: 'Edibles',
    [PRODUCT_CATEGORIES.Extract]: 'Extracts',
    [PRODUCT_CATEGORIES.Flower]: 'Flower',
    [PRODUCT_CATEGORIES.Merch]: 'Merch',
    [PRODUCT_CATEGORIES.Misc]: 'Miscellaneous',
    [PRODUCT_CATEGORIES.Pill]: 'Pill',
    [PRODUCT_CATEGORIES.Plant]: 'Plant',
    [PRODUCT_CATEGORIES.Preroll]: 'PreRoll',
    [PRODUCT_CATEGORIES.Tincture]: 'Tincture',
    [PRODUCT_CATEGORIES.Topical]: 'Topical',
    [PRODUCT_CATEGORIES.Global]: 'Language',
}

const findCategoryIcon = (categoryName: keyof typeof categoryMap): IconName => (categoryMap[categoryName] || 'Release') as IconName;

export default findCategoryIcon;
