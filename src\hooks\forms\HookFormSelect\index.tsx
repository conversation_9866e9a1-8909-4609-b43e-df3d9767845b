import React, {useEffect} from 'react';
import {Controller, useFormContext} from 'react-hook-form';
import {SelectProps} from '@treez-inc/component-library/dist/components/Select';
import {
    styled,
    MenuItem,
    FormControl,
    InputLabel,
    Select,
    type MenuProps as MuiMenuProps,
} from '@mui/material';
import {MenuItemsProps} from '../../../components/Shared/MultiSelectSearch';

export interface HookFormSelectProps extends SelectProps {
    name: string;
    autoSelectDefault?: boolean;
    defaultPropKey?: string;
    selectedMenuItems: string[];
}

const StyledSelect = styled(Select)(({theme}) => ({
    height: '3.25em',
    backgroundColor: theme.palette.treezGrey[3],
    borderRadius: '19px',
    padding: 0,
    display: 'flex',
    alignItems: 'end',
    '&:hover': {
        backgroundColor: theme.palette.treezGrey[3],
    },
    '&:focus': {
        outline: `0.16em solid ${theme.palette.primaryBlack.main}`,
    },
    '&.Mui-disabled': {
        backgroundColor: theme.palette.treezGrey[2],
        outline: 'unset',
    },
    '& .MuiFilledInput-input': {
        paddingLeft: '1rem',
        paddingTop: '1.31em',
        maxHeight: '3.25rem',
        '&:focus': {
            backgroundColor: 'transparent',
            borderRadius: '19px',
            outline: `0.16em solid ${theme.palette.primaryBlack.main}`,
            paddingTop: '1.31em',
        },
    },
    '& .MuiTypography-root': {
        height: '1.25em',
        position: 'relative',
        top: '-0.125em',
    },
}));

const StyledMenuItem = styled(MenuItem)(({theme}) => ({
    '&.MuiMenuItem-root': {
        ...theme.typography.largeText,
        height: '2.75em',
        '&:hover': {
            backgroundColor: theme.palette.treezGreen[3],
        },
        '&.Mui-selected': {
            backgroundColor: theme.palette.treezGreen[3],
        },
        '&:focus-visible': {
            backgroundColor: 'transparent',
            border: `2.5px solid ${theme.palette.primaryBlack.main}`,
        },
    },
}));

const StyledFormControl = styled(FormControl)(({theme}) => ({
    width: '100%',
    '& .MuiFilledInput-root': {
        '&:before': {borderBottom: 'none !important'},
        '&:hover:before': {borderBottom: 'none !important'},
        '&:after': {borderBottom: 'none !important'},
    },

    '& .MuiInputLabel-root': {
        margin: '0em .25em',
        '&.Mui-focused': {
            color: theme.palette.primaryBlack.main,
        },
    },
}));

const menuProps: Partial<MuiMenuProps> = {
    anchorOrigin: {
        horizontal: 'center',
        vertical: 'bottom',
    },
    transformOrigin: {
        horizontal: 'center',
        vertical: 'top',
    },
    sx: {
        'div:nth-of-type(3)': {
            marginTop: '0.125em',
        },
        '& .MuiPopover-paper': {
            backgroundColor: 'grey02.main',
            boxShadow: '0 30px 40px rgba(0, 0, 0, 0.12)',
            borderRadius: '17px',
        },
    },
    disableAutoFocusItem: true,
    MenuListProps: {
        sx: {
            width: '100%',
            padding: '0.625em 0',
        },
    },
};

export default function HookFormSelect({
    name,
    autoSelectDefault,
    defaultPropKey,
    menuItems,
    selectedMenuItems,
    onChange,
    ...props
}: HookFormSelectProps) {
    const {
        control,
        formState: {errors},
        getFieldState,
        getValues,
        setValue,
    } = useFormContext();

    useEffect(() => {
        if (autoSelectDefault) {
            const currentValue = getValues(name);
            if (menuItems && (!currentValue || currentValue === '')) {
                const getDefaultItem = () => {
                    if (menuItems.length === 1) {
                        return menuItems[0];
                    }
                    if (defaultPropKey) {
                        return menuItems.find((m: any) => m[defaultPropKey] === true);
                    }
                    return undefined;
                };
                const defaultItem = getDefaultItem();
                if (defaultItem) {
                    setValue(name, defaultItem.displayValue);
                }
            }
        }
    }, [menuItems]);

    const getError = () => {
        if (errors[name]) {
            return errors[name]?.message?.toString();
        }
        const fieldState = getFieldState(name);
        return fieldState?.error?.message;
    };

    return (
        <div>
            <Controller
                control={control}
                name={name}
                render={({field}) => {
                    const filteredMenuItems = menuItems.filter(
                        (item: MenuItemsProps) =>
                            !selectedMenuItems.includes(item.displayName) ||
                            item.displayValue === field.value
                    );
                    return (
                        <StyledFormControl variant="filled" error={!!getError()}>
                            <InputLabel id={`${name}-label`}>Filter</InputLabel>
                            <StyledSelect
                                {...props}
                                MenuProps={menuProps}
                                labelId={`${name}-label`}
                                label="Filter"
                                onChange={(e, c) => {
                                    const selectedValue = e.target.value as string;
                                    if (selectedMenuItems.includes(selectedValue)) {
                                        return;
                                    }
                                    field.onChange(e);
                                    if (onChange) {
                                        onChange(e, c);
                                    }
                                }}
                                value={field.value ?? ''}
                                data-testid={`input-${name?.toLowerCase().replaceAll(' ', '-')}`}
                            >
                                {filteredMenuItems.map((item) => (
                                    <StyledMenuItem
                                        key={item.displayValue}
                                        value={item.displayValue}
                                    >
                                        {item.displayName}
                                    </StyledMenuItem>
                                ))}
                            </StyledSelect>
                        </StyledFormControl>
                    );
                }}
            />
        </div>
    );
}
