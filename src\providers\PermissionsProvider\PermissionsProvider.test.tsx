import React from 'react';
import '@testing-library/jest-dom';
import {render, waitFor} from '@testing-library/react';
import PermissionsProvider, {PermissionsContext} from '.';

describe('<PermissionsProvider />', () => {
    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    it('updates context with permissions and orgId when getPermissions returns data', async () => {
        const mockGetPermissions = async () => ({
            permissions: ['perm1', 'perm2'],
            id: 'orgId',
            entityContexts: [{id: 'entityId'}],
        });

        render(
            <PermissionsProvider getPermissions={mockGetPermissions}>
                <PermissionsContext.Consumer>
                    {(value) => (
                        <div data-testid="permission-context-values">
                            Permissions:{' '}
                            {value?.permissions ? JSON.stringify(value.permissions) : 'null'} <br />
                            OrgId: {value?.orgId ? value.orgId : 'null'}
                        </div>
                    )}
                </PermissionsContext.Consumer>
            </PermissionsProvider>
        );

        await waitFor(() => {
            expect(
                document.querySelector("[data-testid='permission-context-values']")?.textContent
            ).toContain('Permissions: {"perm1":true,"perm2":true}');
            expect(
                document.querySelector("[data-testid='permission-context-values']")?.textContent
            ).toContain('OrgId: orgId');
        });
    });

    it('does not update context when getPermissions returns null', async () => {
        const mockGetPermissions = async () => null;

        render(
            <PermissionsProvider getPermissions={mockGetPermissions}>
                <PermissionsContext.Consumer>
                    {(value: any) => (
                        <div data-testid="permission-context-values">
                            Permissions:{' '}
                            {value?.permissions ? JSON.stringify(value.permissions) : 'null'} <br />
                            OrgId: {value?.orgId ? value.orgId : 'null'}
                        </div>
                    )}
                </PermissionsContext.Consumer>
            </PermissionsProvider>
        );

        await waitFor(() => {
            expect(
                document.querySelector("[data-testid='permission-context-values']")?.textContent
            ).toContain('Permissions: null');
            expect(
                document.querySelector("[data-testid='permission-context-values']")?.textContent
            ).toContain('OrgId: null');
        });
    });
});
