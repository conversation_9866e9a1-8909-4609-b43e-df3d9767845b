import React from 'react';
import {Box, Paper, styled} from '@mui/material';
import convertPxToRem from '@treez-inc/component-library/dist/util-functions/convertPxToRem';
import {Button} from '@treez-inc/component-library';

const CancelButton = styled(Box)(() => ({
    float: 'left',
    padding: convertPxToRem(16),
    marginLeft: '14%',
}));

const CreateButton = styled(Box)(() => ({
    float: 'right',
    padding: convertPxToRem(16),
    marginRight: '12%',
}));

const StyledFooter = styled(Paper)(() => ({
    position: 'fixed',
    bottom: convertPxToRem(0),
    left: convertPxToRem(0),
    right: convertPxToRem(0),
    zIndex: 99,
}));

interface CollectionFormButtonsProps {
    handleAutomatedCollectionSubmit: (e: React.FormEvent) => Promise<void>;
    isAddAutomatedCollectionDisabled: boolean;
    closeAutomatedCollectionForm: (collection: any) => void;
    submitType: string;
    isDeletedCollection: boolean;
    isUpdatePending: boolean;
    change: boolean,
}

export default function CollectionFormButtons({
    handleAutomatedCollectionSubmit,
    isAddAutomatedCollectionDisabled,
    closeAutomatedCollectionForm,
    submitType,
    isDeletedCollection,
    isUpdatePending,
    change,
}: CollectionFormButtonsProps) {      
    return (
        <StyledFooter elevation={3}>
            <CancelButton>
                <Button
                    label="Cancel"
                    onClick={closeAutomatedCollectionForm}
                    testId="cancel-collection-form-button"
                    variant="secondary"
                />
            </CancelButton>

            <CreateButton>
                <Button
                    label={`${submitType}`}
                    onClick={handleAutomatedCollectionSubmit}
                    testId="create-collection-form-button"
                    variant="primary"
                    disabled={
                        isAddAutomatedCollectionDisabled || 
                        isDeletedCollection || 
                        !isUpdatePending || 
                        !change
                    }
                />
            </CreateButton>
        </StyledFooter>
    );
}
