import {IVariant} from '../../interfaces/variant';

function formatPrice(price: number): string {
    return price ? `$${price.toFixed(2)}` : '-';
}

function priceCentsToDollar(price: number): number {
    return price ? price / 100 : 0;
}

export default function useBasePriceLabel(productActiveVariants: IVariant[]): string {
    const label = '-';

    const variantLength = productActiveVariants.length;

    if (variantLength === 0) {
        return label;
    }

    if (variantLength === 1) {
        const basePrice = priceCentsToDollar(
            parseFloat(productActiveVariants[0]?.defaultPrices?.base as unknown as string)
        );
        return formatPrice(basePrice);
    }

    const basePrices = productActiveVariants
        .map((productVariant: IVariant) =>
            Number(priceCentsToDollar(productVariant?.defaultPrices?.base ?? 0))
        )
        .filter(Boolean);

    if (basePrices.length === 0) {
        return label;
    }

    const min = Math.min(...basePrices);
    const max = Math.max(...basePrices);

    if (min === max) {
        return formatPrice(max);
    }

    return `${formatPrice(min)} - ${formatPrice(max)}`;
}
