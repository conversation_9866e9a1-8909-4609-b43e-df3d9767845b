import React, {useEffect, useState} from 'react';
import {useLocation, useParams} from 'react-router-dom';
import {Breadcrumbs, convertPxToRem} from '@treez-inc/component-library';
import {Box, styled} from '@mui/material';
import PageLayout from '../../components/PageLayout';
import GridLayout from '../../components/GridLayout';
import RouteError from '../../components/RouteError';
import useProductCollectionDetails from '../../hooks/useProductCollectionDetails';
import {
    ProductCollectionDetailsGrid,
    transformProductData,
} from '../../components/ProductCollectionDetailsGrid';
import LoadingSpinner from '../../components/Shared/LoadingSpinner';
import {IProduct} from '../../interfaces/product';

const StyledHeader = styled('div')(({theme}) => ({
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    background: theme.palette.grey02.main,
}));

const StyleBreadcrumbs = styled(Box)(({theme}) => ({
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    alignContent: 'center',
    justifyContent: 'center',
    background: theme.palette.grey02.main,
    padding: `${convertPxToRem(24)} ${convertPxToRem(52)}`,
}));

const ProductCollectionDetails: React.FC = () => {
    const {id = ''} = useParams<{id: string}>();

    const location = useLocation();
    const searchParams = new URLSearchParams(location.search);

    let encodedCollectionName;
    let decodedCollectionName = '';

    try {
        encodedCollectionName = searchParams.get('n');
        decodedCollectionName = encodedCollectionName ? atob(encodedCollectionName) : '';
    } catch (e) {
        console.debug('Error occured on decoding collection name', e);
    }

    const [isLoading, setIsLoading] = useState(false);
    const {data, loading, error, refetch} = useProductCollectionDetails(id);
    const [allProducts, setAllProducts] = useState<IProduct[]>([]);

    if (error !== null) {
        return <RouteError error={error} />;
    }

    useEffect(() => {
        const rowModels =
            data &&
            data?.data?.map((element) => ({
                ...element,
            }));

        if (rowModels && rowModels.length > 0) {
            const updatedProducts: IProduct[] = transformProductData(rowModels) || [];
            setAllProducts(updatedProducts);
        }
    }, [data]);

    return (
        <PageLayout testId="collection-details-page-layout">
            <StyledHeader data-testid="collection-details-header">
                <StyleBreadcrumbs>
                    <Breadcrumbs
                        ariaLabel="Collection Details Breadcrumbs"
                        links={[
                            {
                                ariaLabel: 'Catalog',
                                children: 'Catalog',
                                href: `/product-control`,
                            },
                            {
                                ariaLabel: 'Collections',
                                children: 'Collections',
                                href: `/product-collection`,
                            },
                            {
                                ariaLabel: `${decodedCollectionName}`,
                                children: `${decodedCollectionName}`,
                                href: '',
                            },
                        ]}
                        testId="collection-details-breadcrumbs"
                    />
                </StyleBreadcrumbs>
            </StyledHeader>
            {(loading || isLoading) && <LoadingSpinner />}
            <GridLayout testId="collection-details-grid-layout">
                <ProductCollectionDetailsGrid
                    data={allProducts || []}
                    refetch={refetch}
                    loading={loading}
                    collectionId={id}
                    collectionName={decodedCollectionName}
                    setIsLoading={setIsLoading}
                />
            </GridLayout>
        </PageLayout>
    );
};

export default ProductCollectionDetails;
