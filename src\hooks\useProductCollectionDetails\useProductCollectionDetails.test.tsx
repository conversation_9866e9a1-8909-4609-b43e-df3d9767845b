import {renderHook, waitFor} from '@testing-library/react';
import useProductCollectionDetails from '.';
import {productListData} from '../../test/testData';

jest.mock('../useGetRequest');

const mockUseGetRequest = require('../useGetRequest').default;

const mockState = {
    error: null,
    loading: false,
    refetch: jest.fn(),
    data: productListData,
};

describe('useProductCollectionDetails Hook', () => {
    beforeEach(() => {
        mockUseGetRequest.mockReturnValue(mockState);
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    it('should fetch data and update state on successful API request', async () => {
        const mockApiResponse = productListData;

        mockUseGetRequest.mockReturnValue({
            data: mockApiResponse,
            loading: false,
            error: null,
        });

        const {result} = renderHook(() => useProductCollectionDetails());

        await waitFor(() => {
            expect(result.current.data).toEqual(mockApiResponse);
        });
    });

    it('should handle API request error', async () => {
        mockUseGetRequest.mockReturnValue({
            data: null,
            loading: false,
            error: new Error('API request failed'),
        });

        const {result} = renderHook(() => useProductCollectionDetails());

        await waitFor(() => {
            expect(result.current.error).toEqual(new Error('API request failed'));
        });
    });
});
