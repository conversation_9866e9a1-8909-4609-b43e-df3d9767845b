import {useEffect, useState} from 'react';
import useGetRequest from '../useGetRequest';
import {GetCollectionDetailsResponseBody} from '../../interfaces/apiResponses';
import {COLLECTIONS_URL} from '../../constants/apiEndPoints';

const useProductCollectionDetails = (collectionId?: string) => {
    const [currentCollectionDetailsState, setCurrentCollectionDetailsState] =
        useState<GetCollectionDetailsResponseBody | null>({
            data: [],
        });
    const state = useGetRequest<GetCollectionDetailsResponseBody>(
        `${COLLECTIONS_URL}/product/${collectionId}`
    );

    const {data: response} = state;

    useEffect(() => {
        if (response !== undefined) {
            setCurrentCollectionDetailsState(response);
        }
    }, [response]);

    return {...state, data: currentCollectionDetailsState};
};

export default useProductCollectionDetails;
