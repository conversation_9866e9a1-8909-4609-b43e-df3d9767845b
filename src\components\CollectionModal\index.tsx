import {Modal} from '@treez-inc/component-library';
import React from 'react';

interface IModelButton {
    label: string;
    isDisabled: boolean;
    onClose?: () => void;
    onClick?: (e: React.FormEvent) => void;
}

interface ICollectionModal {
    isOpen: boolean;
    children: React.JSX.Element;
    primaryButton: IModelButton;
    secondaryButton: IModelButton;
    testId: string;
    title: string;
}

const CollectionModal: React.FC<ICollectionModal> = ({
    isOpen,
    children,
    primaryButton,
    secondaryButton,
    testId,
    title,
}) => (
    <Modal
        open={isOpen}
        content={children}
        onClose={secondaryButton.onClose}
        primaryButton={{
            label: primaryButton.label,
            onClick: primaryButton.onClick,
            disabled: primaryButton.isDisabled,
        }}
        secondaryButton={{
            label: secondaryButton.label,
            onClick: secondaryButton.onClose,
        }}
        testId={testId}
        title={title}
    />
);

export default CollectionModal;
