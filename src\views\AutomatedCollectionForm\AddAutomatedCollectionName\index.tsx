import React, { SetStateAction } from 'react';
import Box from '@mui/material/Box';
import {styled} from '@mui/material/styles';
import {convertPxToRem, Input} from '@treez-inc/component-library';
import {AutomatedCollectionFormData, CollectionFormError} from '../../../interfaces/collectionForm';

interface IAddAutomatedCollectionName {
    formData: AutomatedCollectionFormData;
    setFormData: React.Dispatch<React.SetStateAction<AutomatedCollectionFormData>>;
    formError: {
        collectionName: CollectionFormError;
    };
    setFormError: React.Dispatch<SetStateAction<{ id?: CollectionFormError | undefined; collectionName: CollectionFormError; sync: CollectionFormError; rule: CollectionFormError; collectionItems: CollectionFormError; }>>
    setIsUpdatePending: React.Dispatch<React.SetStateAction<any>>;
}

const FlexColumnWithGap = styled(Box)(() => ({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    flexDirection: 'column',
    gap: 16,
    marginTop: convertPxToRem(12),
}));

const AddAutomatedCollectionName: React.FC<IAddAutomatedCollectionName> = ({
    formData: {collectionName},
    setFormData,
    formError,
    setFormError,
    setIsUpdatePending,
}) => {
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setFormData((data: any) => ({...data, collectionName: e.target.value}));
        setIsUpdatePending(true);
        setFormError((prev: any) => ({
            ...prev,
            collectionName: { isError: false, message: '' },
        }));
    };

    return (
        <FlexColumnWithGap data-testid="add-automated-collection-form-field-wrapper">
            <Input
                value={collectionName}
                label="Product collection name"
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    handleInputChange(e);
                }}
                helperText={formError.collectionName.message}
                error={formError.collectionName.isError}
                testId="automated-collection-name-field"
                required
            />
        </FlexColumnWithGap>
    );
};

export default AddAutomatedCollectionName;
