export interface IDefaultPrices {
    base?: number;
    rec?: number;
    med?: number;
    [x: string]: number | undefined;
}

export interface IVariant {
    id: string;
    isExclude: boolean;
    defaultPrices?: IDefaultPrices;
    sku: string;
    uom: string | null;
    amount: number | null;
    unitCount: number;
    merchandiseSize?: string | null;
    name: string;
}

export interface VariantPropertiesProps {
    size: number;
    sizeLabel: string;
    merchandiseSize: string;
}

export interface IVariantIds {
    variantId: string;
}
