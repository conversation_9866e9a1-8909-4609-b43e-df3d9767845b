import React from 'react';
import {fireEvent, render} from '@testing-library/react';
import '@testing-library/jest-dom';
import {TreezThemeProvider} from '@treez-inc/component-library';
import AddCollection from '.';
import {CollectionFormData} from '../../interfaces/collectionForm';

describe('<AddCollection />', () => {
    const formData: CollectionFormData = {
        collectionName: 'test',
    };
    const setFormData = jest.fn();
    const formError = {
        collectionName: {
            isError: false,
            message: '',
        },
    };
    const debounceFunction = jest.fn();

    beforeEach(() => {
        formError.collectionName = {
            isError: false,
            message: '',
        };
        jest.clearAllMocks();
    });

    const renderAddCollectionComponent = () => {
        const {getByTestId} = render(
            <TreezThemeProvider>
                <AddCollection
                    formData={formData}
                    setFormData={setFormData}
                    formError={formError}
                    debounceFunction={debounceFunction}
                />
            </TreezThemeProvider>
        );

        const getComponentWrapper = () => getByTestId('add-collection-form-field-wrapper');
        const collectionNameField = getByTestId('collection-name-field');
        return {
            getComponentWrapper,
            collectionNameField,
        };
    };
    it('should render the component', () => {
        const {getComponentWrapper} = renderAddCollectionComponent();

        expect(getComponentWrapper()).toBeInTheDocument();
    });

    it('should call the debounce function on entering value', () => {
        const {collectionNameField} = renderAddCollectionComponent();

        const inputField = collectionNameField.querySelector('input');
        if (inputField) {
            fireEvent.change(inputField, {target: {value: 'dummyCollection'}});
        }
        expect(debounceFunction).toBeCalledTimes(1);
        expect(debounceFunction).toBeCalledWith('dummyCollection', 'collectionName');
    });

    it('should show error message on form error', () => {
        formError.collectionName = {
            isError: true,
            message: 'This collection name already exists',
        };
        const {collectionNameField} = renderAddCollectionComponent();

        const inputHelperText = collectionNameField.querySelector('p');

        expect(inputHelperText).toBeInTheDocument();
        expect(inputHelperText).toHaveTextContent('This collection name already exists');
    });
});
