
export const priceDollarToCents = (price: number): number => price * 100;

export const priceCentsToDollar = (priceCents: number): number => priceCents / 100;

export const isNil = (value: any): boolean => value === undefined || value === null || value === '';

export const isEmpty = (value: any): boolean => {
    if (isNil(value)) {
        return true;
    }

    if (Array.isArray(value)) {
        return value?.length === 0;
    }

    if (value instanceof Date) {
        return false;
    }

    if (typeof value === 'object') {
        return Object.keys(value).length === 0;
    }

    return false;
};

export const delay = (ms: number): Promise<void> => new Promise(resolve => {
    setTimeout(resolve, ms);
});
