import React, {useEffect, useState} from 'react';
import {convertPxToRem, DataGridPro} from '@treez-inc/component-library';
import {GridColDef, GridColumnHeaderParams, GridCellParams} from '@mui/x-data-grid-pro';
import {Typography, styled, Box, Link} from '@mui/material';
import {useNavigate} from 'react-router-dom';
import IFilterComponent from '../FilterComponent';
import IProductCollection from '../../interfaces/productCollection';
import {filterCollectionList} from '../../utils/filterUtil';
import {CollectionMenu} from '../CollectionMenu';
import usePermissionsContext from '../../hooks/usePermissionsContext';
import {MFEPermissions} from '../../interfaces/permissions';
import {IProductCollectionData} from '../../interfaces/productCollectionResponse';

const StyledCollectionsCount = styled(Typography)(() => ({
    marginBottom: convertPxToRem(0),
}));

const StyledFilterContainer = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: '1rem',
    marginBottom: convertPxToRem(-45),
}));

const StyledCollectionNameCell = styled(Box)(({theme}) => ({
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gridGap: '1rem',
    justifyContent: 'center',
    ':hover': {
        cursor: 'pointer',
        color: theme.palette.green01.main,
    },
}));

const RowLink = styled(Link)(({theme}) => ({
    color: `${theme.palette.secondary.main}`,
    ':hover': {
        color: `${theme.palette.success.main}`,
    },
}));

interface IProductCollectionGrid {
    data: IProductCollectionData[];
    loading: boolean;
    setIsLoading: (loading: boolean) => void;
}

const headerConfig: Partial<GridColDef> = {
    renderHeader: (params: GridColumnHeaderParams) => (
        <Typography variant="mediumTextStrong" color="primaryBlackText">
            {params.colDef.headerName}
        </Typography>
    ),
};

const dateOptions: Intl.DateTimeFormatOptions = {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
};

type NavigateFunction = (to: string, options?: {state?: any}) => void;

const renderActionCellContent = (row: any, setIsLoading: (loading: boolean) => void) => {
    const {deleteCollection, ...restOfRow} = row;
    const {permissions} = usePermissionsContext();
    const hasManagePermissions = permissions?.[MFEPermissions.MANAGE_PRODUCT_COLLECTIONS] === true;

    if (hasManagePermissions && row.deletedAt === null) {
        return <CollectionMenu row={restOfRow} deleteCollection={deleteCollection} setIsLoading={setIsLoading} />;
    }
    return null;
};

const renderNameCellContent = (params: GridCellParams, navigate: NavigateFunction) => {
    let encodedCollectionName: string;

    if (params?.row?.name !== undefined && params?.row?.name !== null) {
        encodedCollectionName = btoa(params.row.name);
    }

    const collectionId = params?.row?.id;

    return (
        <StyledCollectionNameCell>
            <RowLink
                data-testid="collection-name-container"
                onClick={() => {
                        if (params?.row?.sync) {
                            navigate(`/product-collection/automated/${collectionId}`)
                        } else {
                            navigate(`/product-collection/${collectionId}?n=${encodedCollectionName}`)
                        }
                    }
                }
            >
                {params.row.name}
            </RowLink>
        </StyledCollectionNameCell>
    );
};

const useCollectionDetailsColumns = ({
    navigate,
}: {
    navigate: NavigateFunction;
}, setIsLoading: (loading: boolean) => void): Array<GridColDef> => {
    const columns: Array<GridColDef> = [
        {
            field: 'action',
            disableReorder: true,
            sortable: false,
            disableColumnMenu: true,
            align: 'right',
            minWidth: 80,
            maxWidth: 80,
            flex: 1,
            renderHeader: () => null,
            renderCell: ({row}) => renderActionCellContent(row, setIsLoading),
        },
        {
            ...headerConfig,
            flex: 2,
            field: 'name',
            headerName: 'Collection',
            renderCell: (params) => renderNameCellContent(params, navigate),
        },
        {
            ...headerConfig,
            flex: 2,
            field: 'updatedAt',
            headerName: 'Last update',
            valueFormatter: (params) =>
                params.value !== null
                    ? new Date(params.value).toLocaleDateString('en-US', dateOptions)
                    : undefined,
        },
        {
            ...headerConfig,
            flex: 2,
            field: 'sync',
            headerName: 'Type',
            valueFormatter: (params) =>
                params.value ? 'Automated' : 'Manual',
        },
        // {
        //     ...headerConfig,
        //     flex: 2,
        //     field: 'deletedAt',
        //     headerName: 'Status',
        //     valueFormatter: (params) => (params.value !== null ? 'Inactive' : 'Active'),
        // },
    ];

    return columns;
};

let selectedFilter = [] as string[];

const ProductCollectionGrid: React.FC<IProductCollectionGrid> = ({
    data,
    loading,
    setIsLoading,
}) => {
    const navigate = useNavigate();
    const [filteredCollectionList, setFilteredCollectionList] = useState<IProductCollection[]>([]);

    const StyledFilterCollectionBox = styled(Box)(() => ({
        width: '100%',
        paddingBottom: convertPxToRem(24),
        height: filteredCollectionList?.length > 0 ? '100%' : convertPxToRem(530),
    }));

    useEffect(() => {
        setFilteredCollectionList(
            filterCollectionList(data, {
                status: selectedFilter,
            })
        );
    }, [data]);

    const onFilter = (filter: any) => {
        const statusFilter = filter.status;
        selectedFilter = filter.status;

        if (statusFilter && statusFilter.length > 0) {
            const filteredData = filterCollectionList(data, filter);

            setFilteredCollectionList(filteredData);
        } else {
            setFilteredCollectionList(data);
        }
    };

    const columns = useCollectionDetailsColumns({navigate}, setIsLoading);

    return (
        <>
            <StyledFilterContainer>
                <StyledCollectionsCount variant="largeTextStrong" data-testid="collections-count">
                    {filteredCollectionList.length || 0} Collection
                    {filteredCollectionList.length === 1 ? '' : 's'}
                </StyledCollectionsCount>
                <IFilterComponent onChange={onFilter} />
            </StyledFilterContainer>
            <StyledFilterCollectionBox>
                {filteredCollectionList && (
                    <DataGridPro
                        loading={loading}
                        columns={columns}
                        rowCount={filteredCollectionList.length || 0}
                        rows={filteredCollectionList || []}
                        rowSpacingType="border"
                        data-testid="collection-list-grid"
                        autoHeight
                        paginationMode="client"
                        columnBuffer={5}
                        getRowId={(row: IProductCollection) => row?.id}
                    />
                )}
            </StyledFilterCollectionBox>
        </>
    );
};

export default ProductCollectionGrid;
