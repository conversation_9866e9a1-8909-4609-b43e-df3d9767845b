import React, {useState} from 'react';
import {Icon<PERSON>utton, Menu, TextMenuItem} from '@treez-inc/component-library';
import {IconName} from '@treez-inc/component-library/dist/components/Icon/types';
import {useNavigate} from 'react-router-dom';
import IProductCollection from '../../interfaces/productCollection';
import DeleteCollection from '../../views/DeleteCollection';

export interface CollectionMenuProps {
    row: IProductCollection;
    deleteCollection: (collectionId: IProductCollection) => void;
    setIsLoading: (loading: boolean) => void;
}

export const CollectionMenu = ({row, deleteCollection, setIsLoading}: CollectionMenuProps) => {
    const navigate = useNavigate();
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const [isDeleteCollectionModal, setDeleteCollectionModal] = useState(false);

    const open = Boolean(anchorEl);

    let encodedCollectionName: string;

    try {
        encodedCollectionName = btoa(row.name);
    } catch (e) {
        console.debug('Error occurred on encoding collection name', e);
    }

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleModelClose = () => {
        setDeleteCollectionModal(false);
    };

    const handleDeleteCollection = () => {
        setDeleteCollectionModal(true);
        handleClose();
    };

    const handleEditCollections = (collection: IProductCollection) => {
        if (collection.sync) {
            navigate(`/product-collection/automated/${collection.id}`);
        } else {
            navigate(`/product-collection/${collection.id}?n=${encodedCollectionName}`);
        }
    };

    return (
        <>
            <IconButton
                iconName="More"
                onClick={handleClick}
                variant="secondary"
                testId="collections-flyout-menu-more"
            />
            <Menu
                menuId="collections-flyout"
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                testId="collection-action-menu"
            >
                <TextMenuItem
                    displayName="Edit"
                    displayValue="Edit Collection"
                    testId="collection-action-edit"
                    iconProps={{iconName: 'Edit' as IconName}}
                    onClick={() => handleEditCollections(row)}
                />
                <TextMenuItem
                    displayName="Delete"
                    displayValue="Delete Collection"
                    testId="collection-action-delete"
                    iconProps={{iconName: 'Delete' as IconName}}
                    onClick={handleDeleteCollection}
                />
            </Menu>
            {isDeleteCollectionModal && (
                <DeleteCollection
                    collection={row}
                    deleteCollection={deleteCollection}
                    closeModel={handleModelClose}
                    setIsLoading={setIsLoading}
                />
            )}
        </>
    );
};
