import React from 'react';
import { Box, Grid, styled } from '@mui/material';
import { allColors, convertPxToRem, Icon } from '@treez-inc/component-library';
import findCategoryIcon from '../../../utils/categoryUtils';

const CategoryContainer = styled(Grid)`
margin-top: ${convertPxToRem(10)};
`;
const CategoryButton = styled(Box)`
    display:flex,
    flex-direction:column,
    justify-content:center,
    align-items:center,
    cursor: pointer;
    height: 5.625rem;
    padding: 0.375rem 0.375rem 0;
    width: 3.75rem;
    border: 0.125rem solid #eee;
    border-radius: 0.75rem;
    &:hover {
        background-color: #eee;
        border-radius: 0.75rem;
    }
`;
const CategoryImage = styled(Box)`
    height: 2.188rem;
    width: 2.188rem;
    padding: 0.75rem 0.75rem 0;
    text-align: center;
    background-color: #eee;
    border-radius: 0.5rem;
    cursor: pointer;
`;
const CategoryName = styled(Box)`
    color: 'grey9';
    display: inline-block;
    font-family: 'Roboto';
    font-size: 0.625rem;
    font-style: normal;
    font-weight: 600;
    height: 1.25rem;
    letter-spacing: 0.015em;
    line-height: 0.75rem;
    text-align: center;
    width: 2.813rem;
    padding: 0.75rem 0.375rem 0;
    cursor: pointer;
`;

const ProductCategories = React.forwardRef(({ parentCategoriesList, collectionProductCategory, onCategoryClick }: any, ref) => (
        <>
            <CategoryContainer data-testid="product-categories-container" container spacing={1}>
                {parentCategoriesList.map((category: any) => (
                    <Grid key={category.id} item sm={0}>
                        <CategoryButton
                            ref={ref}
                            data-testid={`category-button-${category.name}`}
                            onClick={() => {
                                onCategoryClick(category);
                            }}
                            style={{
                                backgroundColor: collectionProductCategory?.id === category.id ? allColors.green06.main : '',
                            }}
                        >
                            <CategoryImage
                                style={{
                                    backgroundColor: collectionProductCategory?.id === category.id ? 'white' : '',
                                }}>
                                <Icon
                                    color="grey10"
                                    fontSize="large"
                                    iconName={findCategoryIcon(category.name)}
                                />
                            </CategoryImage>
                            <CategoryName>{category.name?.toUpperCase()}</CategoryName>
                        </CategoryButton>
                    </Grid>
                ))}
            </CategoryContainer>
        </>
    )
);

export default ProductCategories;
