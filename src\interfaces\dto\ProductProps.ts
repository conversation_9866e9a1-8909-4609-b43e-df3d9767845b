import { IconName } from '@treez-inc/component-library/dist/components/Icon/types';
import { BrandDto } from './brand';
import { ProductDto } from './product';
import { MenuItemsProps } from './productControl';

export interface ProductFormProps {
    productName?: string;
    brand?: string;
    subCat?: string;
    uom?: string;
    strain?: string;
    status?: string;
}

export const emptyForm: ProductFormProps = {
    productName: '',
    brand: '',
    subCat: '',
    uom: '',
    strain: '',
    status: '',
};

export const emptyNewProduct: ProductDto = {
    name: '',
    status: '',
};

export interface ProductFormValidityProps {
    productNameError: boolean;
    brandError: boolean;
    subCatError: boolean;
    uomError: boolean;
    strainError: boolean;
    statusError: boolean;
}

export const initialProductFormValidity: ProductFormValidityProps = {
    productNameError: true,
    brandError: true,
    subCatError: true,
    uomError: true,
    strainError: true,
    statusError: true,
};

export interface ProductCategoryProps {
    id?: string;
    name?: string;
    parentId?: string;
    image?: any;
    uom?: string[];
}

export const emptyProductCategory: ProductCategoryProps = {
    id: '',
    name: '',
    parentId: '',
};

export interface ProductAttributeMenuItemProps extends MenuItemsProps {
    productAttributeId?: string;
    id: string;
    name: string;
}

export interface SizeVariantProps extends MenuItemsProps {
    variantId?: string;
}

export interface VariantsDataProps {
    categoryName: string;
    uom: string[];
    size: SizeVariantProps[];
    sizeLabel: string;
}

export interface ProductInfoSectionProps {
    id?: string;
    status: string;
    productCategoryId: string;
    productCategoryName?: string;
    name: string;
    brandId?: string;
    productSubCategoryId: string;
    description?: string;
    uom: string;
    classification?: any;
    strain?: string;
    extractionMethod?: string;
}

export interface VariantsInfoSectionProps {
    size: SizeVariantProps[];
    merchandiseSize: SizeVariantProps[];
    sizeLabel: string;
}

export interface ProductInfoWrapperProps {
    product: Partial<ProductInfoSectionProps>;
    variants?: Partial<VariantsInfoSectionProps>;
}

export interface ProductDetailsFieldsProps {
    classification?: string;
    strain?: string;
    thcPerDose?: number;
    cbdPerDose?: number;
    extractionMethod?: string;
    size?: number;
    merchandiseSize?: string;
    totalMgThc?: number;
    totalFlowerWeight?: number;
    totalConcentrateWeight?: number;
    totalMgCbd?: number;
    doses?: number;
    grossWeight?: number;
    netWeight?: number;
    netWeightUom?: string;
}

export interface FilteredProductProps extends ProductDto {
    productCategoryIcon?: IconName;
    productCategory?: { id?: string; name?: string };
    brand?: BrandDto;
}
