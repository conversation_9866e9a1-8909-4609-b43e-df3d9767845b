import {AxiosResponse} from 'axios';
import IProductCollection from './productCollection';
import {IProduct} from './product';
import { AutomatedCollectionDetails } from './collectionForm';

export interface GetAllCollectionsResponseBody {
    totalCount: number;
    data: IProductCollection[];
}

export type ProductCollectionGetAllApiResponse = AxiosResponse<GetAllCollectionsResponseBody>;

export type ProductCollectionGetApiResponse = AxiosResponse<IProductCollection>;

export interface GetCollectionDetailsResponseBody {
    data: IProduct[];
}

export interface GetAutomatedCollectionDetailsResponseBody {
    data: AutomatedCollectionDetails;
}
