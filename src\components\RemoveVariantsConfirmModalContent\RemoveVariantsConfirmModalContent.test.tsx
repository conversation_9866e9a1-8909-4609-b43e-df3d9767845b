import React from 'react';
import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import ApiService from '../../utils/apiService';
import { productListData } from '../../test/testData';
import { transformProductData } from '../ProductCollectionDetailsGrid';
import { RemoveVariantsConfirmModalContent } from '.';

jest.mock('../../hooks/usePermissionsContext');
jest.mock('../../hooks/useApiContext');
jest.mock('../../hooks/useSnackbarContext');

const mockUseApiContext = require('../../hooks/useApiContext').default;
const mockUseSnackbarContext = require('../../hooks/useSnackbarContext').default;

const mockSetSnackbar = jest.fn();

const validTokens = {
    accessToken: 'accessToken',
    expiresIn: 300,
    refreshToken: 'refreshToken',
    idToken: 'idToken',
};
const clearTokens = jest.fn();
const getTokens = () => validTokens;
const redirectToLogin = jest.fn();
const apiService = new ApiService(getTokens, clearTokens, redirectToLogin);

const mockProductListData = transformProductData(productListData.data);

describe('RemoveVariantsConfirmModalContent', () => {
    beforeEach(() => {
        jest.clearAllMocks();

        mockUseApiContext.mockImplementation(() => apiService);
        mockUseSnackbarContext.mockReturnValue({
            setSnackbar: mockSetSnackbar,
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    it('renders content for removing all products', () => {
        render(
            <RemoveVariantsConfirmModalContent
                collectionName="Test Collection"
                selectionModel={[]}
                modifiedData={[]}
                selectedProductsCount={0}
            />
        );
        expect(screen.getByTestId('modal-content-remove-all-products')).toBeInTheDocument();
    });

    it('renders content for removing products', () => {
        render(
            <RemoveVariantsConfirmModalContent
                collectionName="Test Collection"
                selectionModel={[
                    '7c2ea386-923e-4801-aaaf-9a5d2d4e450a',
                    '83d519f1-dc34-4d49-a099-f8dd4d9bdaab',
                ]}
                modifiedData={mockProductListData}
                selectedProductsCount={2}
            />
        );
        expect(screen.getByTestId('modal-content-remove-products')).toBeInTheDocument();
    });

    it('renders content for removing one product', () => {
        render(
            <RemoveVariantsConfirmModalContent
                collectionName="Test Collection"
                selectionModel={['7c2ea386-923e-4801-aaaf-9a5d2d4e450a']}
                modifiedData={mockProductListData}
                selectedProductsCount={1}
            />
        );
        expect(screen.getByTestId('modal-content-remove-one-product')).toBeInTheDocument();
    });

    it('renders content for removing some products', () => {
        render(
            <RemoveVariantsConfirmModalContent
                collectionName="Test Collection"
                selectionModel={[
                    '7c2ea386-923e-4801-aaaf-9a5d2d4e450a',
                    '83d519f1-dc34-4d49-a099-f8dd4d9bdaab',
                    '358c7c40-1cfb-4c19-8fda-a3f06e316c93',
                ]}
                modifiedData={mockProductListData}
                selectedProductsCount={2}
            />
        );
        expect(screen.getByTestId('modal-content-remove-some-products')).toBeInTheDocument();
    });
});
