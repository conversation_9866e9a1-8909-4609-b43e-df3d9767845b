import React from 'react';
import Box from '@mui/material/Box';
import {styled} from '@mui/material/styles';
import {convertPxToRem, Input} from '@treez-inc/component-library';
import {CollectionFormData, CollectionFormError} from '../../interfaces/collectionForm';

interface IAddCollection {
    formData: CollectionFormData;
    setFormData: React.Dispatch<React.SetStateAction<CollectionFormData>>;
    formError: {
        collectionName: CollectionFormError;
    };
    debounceFunction: (value: string, key: string) => Promise<void>;
}

const FlexColumnWithGap = styled(Box)(() => ({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    flexDirection: 'column',
    gap: 16,
    marginTop: convertPxToRem(2),
}));

const AddCollection: React.FC<IAddCollection> = ({
    formData: {collectionName},
    setFormData,
    formError,
    debounceFunction,
}) => {
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setFormData((data) => ({...data, collectionName: e.target.value}));
    };

    return (
        <FlexColumnWithGap data-testid="add-collection-form-field-wrapper">
            <Input
                value={collectionName}
                label="Product collection name"
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    handleInputChange(e);
                    debounceFunction(e.target.value, 'collectionName');
                }}
                helperText={formError.collectionName.message}
                error={formError.collectionName.isError}
                testId="collection-name-field"
            />
        </FlexColumnWithGap>
    );
};

export default AddCollection;
