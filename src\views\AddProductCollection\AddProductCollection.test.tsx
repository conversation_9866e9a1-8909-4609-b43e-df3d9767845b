import React from "react";
import { fireEvent, render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import { TreezThemeProvider } from "@treez-inc/component-library";
import AddProductCollection from ".";
import SnackbarProvider from "../../providers/SnackbarProvider";
import ApiService from "../../utils/apiService";

const closeModalMock = jest.fn();
const mockedUseNavigate = jest.fn();
jest.mock('../../hooks/useApiContext');
jest.mock('react-router-dom', () => ({
    useNavigate: () => mockedUseNavigate,
}));
const setIsLoading = jest.fn();
const mockUseApiContext = require('../../hooks/useApiContext').default;

const productCollectionData = [
    {
      id: 'd393a44a-ecfe-4084-9554-a20bc59d7d59',
      name: 'dummyCollection<PERSON>',
      createdAt: '2023-11-20T08:00:50.864Z',
      updatedAt: '2023-11-20T08:00:50.864Z',
      deletedAt: null,
      sync: false
    },
    {
      id: 'd393a44a-ecfe-4084-9554-a20bc59d7d58',
      name: 'dummyCollectionB',
      createdAt: '2023-11-20T08:00:50.864Z',
      updatedAt: '2023-11-20T08:00:50.864Z',
      deletedAt: null,
      sync: false
  
    },
    {
      id: 'd393a44a-ecfe-4084-9554-a20bc59d7d58',
      name: 'dummyCollectionC',
      createdAt: '2023-11-20T08:00:50.864Z',
      updatedAt: '2023-11-20T08:00:50.864Z',
      deletedAt: '2023-11-20T08:00:50.864Z',
      sync: false,
    },
    {
      id: 'd393a44a-ecfe-4084-9554-a20bc59d7d58',
      name: 'TesingMaxLengthNameForCollectionNameInTheModalAddCollectionProductCollection',
      createdAt: '2023-11-20T08:00:50.864Z',
      updatedAt: '2023-11-20T08:00:50.864Z',
      deletedAt: null,
      sync: false
    }
];

const validTokens = {
    accessToken: 'accessToken',
    expiresIn: 300,
    refreshToken: 'refreshToken',
    idToken: 'idToken',
};
const clearTokens = jest.fn();
const getTokens = () => validTokens;
const redirectToLogin = jest.fn();
const apiService = new ApiService(getTokens, clearTokens, redirectToLogin);

describe('<AddProductCollection/>', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockUseApiContext.mockImplementation(() => apiService);
    });
    const openManualProductCollectionModalMock = jest.fn();
    const renderAddCollectionModalComponent = () => {
        render(
            <TreezThemeProvider>
                <SnackbarProvider>
                    <AddProductCollection
                        closeModal={closeModalMock}
                        productCollectionData={productCollectionData}
                        setIsLoading={setIsLoading}
                    />
                </SnackbarProvider>
            </TreezThemeProvider>,
        );
    };

    it("should render component", () => {
        renderAddCollectionModalComponent();
        expect(screen.getByText("Users can create two types of collections -- Manual & Automated")).toBeInTheDocument();
        expect(screen.getByText("Manual collections are built buy finding a specific product in product control and selecting an action to add it to a manual collection.")).toBeInTheDocument();
        expect(screen.getByText("Automated collections are built by creating a set of rules that products must meet to be in the collection. Any new products that meet those rules are added to the collection by default, but can be explicit excluded in the UI.")).toBeInTheDocument();
        expect(screen.getByRole("radio", { name: "Manual" })).toBeInTheDocument();
        expect(screen.getByRole("radio", { name: "Automated" })).toBeInTheDocument();
        expect(screen.getByTestId("primaryButton-add-collection-modal")).toBeInTheDocument();
        expect(screen.getByTestId("secondaryButton-add-collection-modal")).toBeInTheDocument();
    });

    it("should not open manual collection modal in case of collection type as automated", () => {
        renderAddCollectionModalComponent();
        const automatedRadioButton = screen.getByRole("radio", { name: "Automated" });
        fireEvent.click(automatedRadioButton);
        const primaryButton = screen.getByTestId("primaryButton-add-collection-modal")
        fireEvent.click(primaryButton);
        expect(openManualProductCollectionModalMock).not.toHaveBeenCalled();
    });

    it("should call close modal on click of cancel", () => {
        renderAddCollectionModalComponent();
        const cancelButton = screen.getByTestId("secondaryButton-add-collection-modal")
        fireEvent.click(cancelButton);
        expect(closeModalMock).toHaveBeenCalled();
    });
});
