import React from 'react';
import { act, fireEvent, render, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { TreezThemeProvider } from '@treez-inc/component-library';
import DeleteCollection from '.';
import IProductCollection from '../../interfaces/productCollection';
import SnackbarProvider from '../../providers/SnackbarProvider';
import { COLLECTIONS_URL } from '../../constants/apiEndPoints';

jest.mock('../../hooks/useApiContext');

const mockUseApiContext = require('../../hooks/useApiContext').default;

const closeCollectionModal = jest.fn();
const deleteCollection = jest.fn();
const setIsLoading = jest.fn();
const mockApiService = {
    api: {
        delete: jest.fn(),
    },
};

const deleteCollectionData: IProductCollection = {
    id: '5f391156-0000-0000-0000-ab7e8fa6532a',
    name: 'test name',
    sync: false,
    createdAt: '2023-11-06T12:50:03.584Z',
    updatedAt: '2023-11-06T12:50:03.584Z',
    deletedAt: null,
};

const apiDeleteSpy = jest.spyOn(mockApiService.api, 'delete');

describe('<DeleteCollection />', () => {
    beforeEach(() => {
        jest.clearAllMocks();

        mockUseApiContext.mockImplementation(() => mockApiService);
    });

    const renderDeleteCollectionModalComponent = () => {
        const { getByTestId } = render(
            <TreezThemeProvider>
                <SnackbarProvider>
                    <DeleteCollection
                        collection={deleteCollectionData}
                        deleteCollection={deleteCollection}
                        closeModel={closeCollectionModal}
                        setIsLoading={setIsLoading}
                    />
                </SnackbarProvider>
            </TreezThemeProvider>
        );

        const deleteCollectionModel = getByTestId('delete-collection-modal');
        const primaryButton = getByTestId('primaryButton-delete-collection-modal');
        const secondaryButton = getByTestId('secondaryButton-delete-collection-modal');

        return {
            deleteCollectionModel,
            primaryButton,
            secondaryButton,
        };
    };

    it('should render delete collection model component', () => {
        const { deleteCollectionModel } = renderDeleteCollectionModalComponent();

        expect(deleteCollectionModel).toBeInTheDocument();
    });

    it('should render delete button on collection model component', () => {
        const { primaryButton } = renderDeleteCollectionModalComponent();

        expect(primaryButton).toBeInTheDocument();
    });

    it("should render label 'delete' button on collection model component", () => {
        const { primaryButton } = renderDeleteCollectionModalComponent();

        expect(primaryButton).toBeInTheDocument();
        expect(primaryButton).toHaveTextContent('Delete');
    });

    it("should render label 'cancel' button on collection model component", () => {
        const { secondaryButton } = renderDeleteCollectionModalComponent();

        expect(secondaryButton).toBeInTheDocument();
        expect(secondaryButton).toHaveTextContent('Cancel');
    });

    it('should call delete api when delete Collection button is clicked', async () => {
        const dummyResult: IProductCollection = {
            id: '5f391156-0000-0000-0000-ab7e8fa6532a',
            name: 'test name',
            sync: false,
            createdAt: '2023-11-06T12:50:03.584Z',
            updatedAt: '2023-11-06T12:50:03.584Z',
            deletedAt: '2023-11-06T12:50:03.584Z',
        };
        jest.spyOn(mockApiService.api, 'delete').mockResolvedValue({ data: dummyResult });
        const { primaryButton } = renderDeleteCollectionModalComponent();

        await act(async () => {
            fireEvent.click(primaryButton);
            await waitFor(() => {
                expect(mockApiService.api.delete).toBeCalledTimes(1);
                expect(apiDeleteSpy).toHaveBeenCalledWith(
                    `${COLLECTIONS_URL}/${deleteCollectionData.id}`
                );
                expect(deleteCollection).toBeCalledTimes(1);
                expect(closeCollectionModal).toBeCalledTimes(1);
            });
        });
    });
});
