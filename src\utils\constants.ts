/* eslint-disable */
const PRODUCT_MANAGEMENT_SERVICE_API_URL = process.env.PRODUCT_MANAGEMENT_SERVICE_API_URL;
const PRODUCT_COLLECTION_SERVICE_API_URL = process.env.PRODUCT_COLLECTION_SERVICE_API_URL;

const STAGE = process.env.STAGE;

const IMAGE_CDN_URL = `https://cdn.${STAGE === 'prod' ? 'mso' : STAGE}.treez.io`;

// NOTE: These are replaced at compile time
export const VERSION_TAG = '_version_tag_';
export const VERSION_COMMIT = '_version_commit_';
export const VERSION_BUILD_DATE = '_version_build_date_';

export default { PRODUCT_MANAGEMENT_SERVICE_API_URL, STAGE, IMAGE_CDN_URL, PRODUCT_COLLECTION_SERVICE_API_URL };

export const DEBOUNCE_TIME = 500;
// Static Chips Color
export const chipColor: any = {
    Effects: 'gray',
    Aroma: 'yellow',
    Flavor: 'orange',
    Internal: 'peach',
    General: 'blue',
    Ingredients: 'purple',
};

// Common fields for variant types (Sample or Promo)
export const variantDetailFields = [
    'totalMgCbd',
    'totalMgThc',
    'totalFlowerWeight',
    'usableMarijuanaWeight',
    'doses',
    'cbdPerDose',
    'thcPerDose',
    'grossWeight',
    'netWeight',
    'netWeightUom',
    'totalConcentrateWeight',
];

// categories - categoryFields - fields that are shown for each category
//  interface / type / structure: { "category(enum?): string": { ["fieldName(enum?): string"]: boolean }}

export const categories: any = {
    pill: {
        classification: true,
        strain: true,
        size: true,
        totalMgThc: true,
        totalMgCbd: true,
        doses: true,
        thcPerDose: true,
        cbdPerDose: true,
        totalFlowerWeight: true,
        netWeight: true,
        netWeightUom: true,
    },
    tincture: {
        classification: true,
        strain: true,
        size: true,
        totalMgThc: true,
        totalMgCbd: true,
        doses: true,
        thcPerDose: true,
        cbdPerDose: true,
        totalFlowerWeight: true,
        netWeight: true,
        netWeightUom: true,
    },
    cartridge: {
        classification: true,
        strain: true,
        extractionMethod: true,
        size: true,
        totalMgThc: true,
        totalMgCbd: true,
        totalFlowerWeight: true,
        netWeight: true,
        netWeightUom: true,
    },
    misc: {
        classification: true,
        strain: true,
        size: true,
        totalMgThc: true,
        totalMgCbd: true,
        doses: true,
        thcPerDose: true,
        cbdPerDose: true,
        totalFlowerWeight: true,
        netWeight: true,
        netWeightUom: true,
    },
    preroll: {
        classification: true,
        strain: true,
        size: true,
        totalMgThc: true,
        totalMgCbd: true,
        netWeight: true,
        netWeightUom: true,
        totalFlowerWeight: true,
        totalConcentrateWeight: true,
    },
    flower: {
        classification: true,
        strain: true,
        size: true,
        totalMgThc: true,
        totalMgCbd: true,
        netWeight: true,
        netWeightUom: true,
        totalFlowerWeight: true,
        totalConcentrateWeight: true,
    },
    extract: {
        classification: true,
        strain: true,
        extractionMethod: true,
        size: true,
        totalMgThc: true,
        totalMgCbd: true,
        totalFlowerWeight: true,
    },
    edible: {
        classification: true,
        strain: true,
        size: true,
        totalMgThc: true,
        totalMgCbd: true,
        doses: true,
        thcPerDose: true,
        cbdPerDose: true,
        totalFlowerWeight: true,
        netWeight: true,
        netWeightUom: true,
    },
    beverage: {
        classification: true,
        strain: true,
        size: true,
        totalMgThc: true,
        totalMgCbd: true,
        doses: true,
        thcPerDose: true,
        cbdPerDose: true,
        totalFlowerWeight: true,
        netWeight: true,
        netWeightUom: true,
    },
    plant: {
        classification: true,
        strain: true,
        size: true,
    },
    topical: {
        classification: true,
        strain: true,
        size: true,
        totalMgThc: true,
        totalMgCbd: true,
        doses: true,
        thcPerDose: true,
        cbdPerDose: true,
        totalFlowerWeight: true,
        netWeight: true,
        netWeightUom: true,
    },
    merch: {
        merchandiseSize: true,
    },
    noninv: {
        size: true,
    },
};

export enum RegionType {
    Alabama = 'Alabama',
    Alaska = 'Alaska',
    Arizona = 'Arizona',
    Arkansas = 'Arkansas',
    California = 'California',
    Colorado = 'Colorado',
    Connecticut = 'Connecticut',
    Delaware = 'Delaware',
    DistrictOfColumbia = 'District of Columbia',
    Florida = 'Florida',
    Georgia = 'Georgia',
    Hawaii = 'Hawaii',
    Idaho = 'Idaho',
    Illinois = 'Illinois',
    Indiana = 'Indiana',
    Iowa = 'Iowa',
    Kansas = 'Kansas',
    Kentucky = 'Kentucky',
    Louisiana = 'Louisiana',
    Maine = 'Maine',
    Maryland = 'Maryland',
    Massachusetts = 'Massachusetts',
    Michigan = 'Michigan',
    Minnesota = 'Minnesota',
    Mississippi = 'Mississippi',
    Montana = 'Montana',
    Nevada = 'Nevada',
    NewHampshire = 'New Hampshire',
    NewJersey = 'New Jersey',
    NewMexico = 'New Mexico',
    NewYork = 'New York',
    NorthCarolina = 'North Carolina',
    NorthDakota = 'North Dakota',
    Ohio = 'Ohio',
    Oklahoma = 'Oklahoma',
    Oregon = 'Oregon',
    Pennsylvania = 'Pennsylvania',
    RhodeIsland = 'Rhode Island',
    SouthCarolina = 'South Carolina',
    SouthDakota = 'South Dakota',
    Tennessee = 'Tennessee',
    Texas = 'Texas',
    Utah = 'Utah',
    Vermont = 'Vermont',
    Virginia = 'Virginia',
    VirginIslands = 'Virgin Islands',
    Washington = 'Washington',
    WestVirginia = 'West Virginia',
    Wisconsin = 'Wisconsin',
    Wyoming = 'Wyoming',
}

export const statusMap: any = {
    active: 'Active',
    draft: 'Draft',
    inactive: 'Deactivated',
};

export const sizeLabelMap: any = {
    grams: 'g',
    milligrams: 'mg',
    liters: 'l',
    milliliters: 'ml',
    'fluid ounces': 'fl oz',
    ounces: 'oz',
};

export const merchandiseSizeMap: any = {
    small: 'S',
    medium: 'M',
    large: 'L',
    'one size': 'O/S',
};

export const GLOBAL_CATEGORY_NAME = "Global";

export const globalCategory: any = {
    "id": "global",
    "name": GLOBAL_CATEGORY_NAME,
    "uoms": [
        {
            "label": "GRAMS",
            "value": "GRAMS"
        },
        {
            "label": "MILLIGRAMS",
            "value": "MILLIGRAMS"
        },
        {
            "label": "LITERS",
            "value": "LITERS"
        },
        {
            "label": "MILLILITERS",
            "value": "MILLILITERS"
        },
        {
            "label": "OUNCES",
            "value": "OUNCES"
        },
        {
            "label": "FLUID OUNCES",
            "value": "FLUID OUNCES"
        },
    ],
}

export const EXCLUDED_SUB_CATEGORY_BY_CATEGORY: any = {
    'Edible': ['Capsule', 'Micro-Dose Mints', 'Sublingual Tablet', 'Tablingual', 'Tincture', 'Edible General', 'Edible - General'],
    'Beverage': ['Beverage General', 'Beverage - General'],
    'Cartridge': ['Pax Pod', 'CCELL', 'C', 'Diamond', 'Diamond Line', 'Gem', 'Gem Line', 'Pax', 'Distilate', 'Full Spectrum', 'CO2', 'Cartridge General', 'Cartridge - General'],
    'CBD': ['General', 'CBD - General'],
    'Extract': ['Full Spectrum Oil', '1st Press Rosin', '2nd Press Rosin', 'L.S 1st Press Rosin', 'Living Soil', 'Rosin Sauce', 'Temple Ball', 'Temple Ball Hash', 'Water Hash', 'Extract General', 'Extract - General'],
    'Flower': ['Infused Flower', 'Flower General', 'Flower - General'],
    'Merch': ['Merch General', 'Merch - General'],
    'Misc': ['Misc General', 'Misc - General'],
    'Non-Inv': ['Non-Inv General', 'Non-Inv - General'],
    'Pill': ['Pill General', 'Pill - General'],
    'Plant': ['Plant General', 'Plant - General'],
    'Preroll': ['Infused Preroll', 'Preroll General', 'Preroll - General'],
    'Tincture': ['Tincture General', 'Tincture - General'],
    'Topical': ['Topical General', 'Topical - General']
}