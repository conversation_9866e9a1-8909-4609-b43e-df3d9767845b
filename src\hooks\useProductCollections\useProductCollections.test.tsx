import { act, renderHook, waitFor } from '@testing-library/react';
import useProductCollections from '.';
import { productCollectionListData } from '../../test/testData';

jest.mock('../useGetRequest');

const mockUseGetRequest = require('../useGetRequest').default;

const mockState = {
    error: null,
    loading: false,
    refetch: jest.fn(),
    data: productCollectionListData,
};

const testCollection = {
    id: 'd393a44a-ecfe-4084-9554-a20bc59d7d59',
    name: 'dummyCollection',
    createdAt: '2023-11-20T08:00:50.864Z',
    updatedAt: '2023-11-20T08:00:50.864Z',
    deletedAt: '',
    sync: false,
};

describe('useProductCollections Hook', () => {
    beforeEach(() => {
        mockUseGetRequest.mockReturnValue(mockState);
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.resetAllMocks();
    });

    it('should fetch data and update state on successful API request', async () => {
        const mockApiResponse = productCollectionListData;

        mockUseGetRequest.mockReturnValue({
            data: mockApiResponse,
            loading: false,
            error: null,
        });

        const { result } = renderHook(() => useProductCollections());

        await waitFor(() => {
            expect(result.current.data).toEqual(mockApiResponse);
        });
    });

    it('should handle API request error', async () => {
        mockUseGetRequest.mockReturnValue({
            data: null,
            loading: false,
            error: new Error('API request failed'),
        });

        const { result } = renderHook(() => useProductCollections());

        await waitFor(() => {
            expect(result.current.error).toEqual(new Error('API request failed'));
        });
    });

    it('should append collection', async () => {
        mockUseGetRequest.mockReturnValue({
            data: mockState.data,
            loading: false,
            error: null,
        });

        const { result } = renderHook(() => useProductCollections());

        act(() => {
            result.current.appendCollection(testCollection);
        });

        await waitFor(() => {
            expect(result.current.data).toEqual({
                totalCount: mockState.data.totalCount + 1,
                data: [testCollection, ...mockState.data.data],
            });
        });
    });

    it('should update collection', async () => {
        mockUseGetRequest.mockReturnValue({
            data: mockState.data,
            loading: false,
            error: null,
        });

        const { result } = renderHook(() => useProductCollections());
        const updatedCollection = {...testCollection, name: 'Update collection name'};

        act(() => {
            result.current.appendCollection(testCollection);
            result.current.appendCollection(updatedCollection);
        });
        
        await waitFor(() => {
            expect(result.current.data).toEqual({
                totalCount: mockState.data.totalCount + 1,
                data: [updatedCollection, ...mockState.data.data],
            });
        });
    });

    it('should deactivate collection', async () => {
        mockUseGetRequest.mockReturnValue({
            data: mockState,
            loading: false,
            error: null,
        });

        const { result } = renderHook(() => useProductCollections());
        const dummyCollection = productCollectionListData.data[0];

        await act(async () => {
            dummyCollection.deletedAt = '2023-11-20T08:00:50.864Z';
            result.current.deleteCollection(dummyCollection);
        });
        expect(mockState.data.data[0].name).toEqual(dummyCollection.name);
        expect(mockState.data.data[0].deletedAt).toBe('2023-11-20T08:00:50.864Z');
    });

    it("should update collection's deletedAt and updatedAt fields", async () => {
        mockUseGetRequest.mockReturnValue({
            data: mockState,
            loading: false,
            error: null,
        });

        const { result } = renderHook(() => useProductCollections());
        const dummyCollection = productCollectionListData.data[0];

        await act(async () => {
            dummyCollection.deletedAt = '2023-11-20T08:00:50.864Z';
            dummyCollection.updatedAt = '2023-11-21T08:00:50.864Z';
            result.current.deleteCollection(dummyCollection);
        });

        expect(mockState.data.data[0].deletedAt).toBe('2023-11-20T08:00:50.864Z');
        expect(mockState.data.data[0].updatedAt).toBe('2023-11-21T08:00:50.864Z');
    });

    it("should update collection's deletedAt and updatedAt fields", async () => {
        mockUseGetRequest.mockReturnValue({
            data: mockState,
            loading: false,
            error: null,
        });

        const { result } = renderHook(() => useProductCollections());

        const dummyCollection = {
            id: '91787d3b-f7c1-4f99-a25e-b021ba899e9d',
            name: 'Collection1',
            organizationId: '8ca822f9-4915-41db-a9ba-9d7eb63cad33',
            sync: false,
            createdAt: '2023-11-06T12:55:27.527Z',
            updatedAt: '2024-01-06T12:55:57.527Z',
            deletedAt: '2024-01-06T12:55:57.527Z',
        };

        act(() => {
            result.current.deleteCollection(dummyCollection);
        });

        const updatedCollection = result.current.data?.data.find(
            (item) => item.id === dummyCollection.id
        );

        if (updatedCollection) {
            expect(updatedCollection.deletedAt).toBe(dummyCollection.deletedAt);
            expect(updatedCollection.updatedAt).toBe(dummyCollection.updatedAt);
        }
    });
});
