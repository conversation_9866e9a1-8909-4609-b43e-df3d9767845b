import { VariantDto } from "../interfaces/dto/variant";

export const getVariantAmountLabel = (variant: VariantDto) => {
    const { details } = variant;
    const isSample: boolean | undefined = details?.isSample;
    const isPromo: boolean | undefined = details?.isPromo;

    let variantLabel = `${variant?.amount ? variant.amount : ''} ${variant?.uom ? variant.uom : ''}`.trim();

    if (isSample) {
        variantLabel += variantLabel ? ` - Sample` : `Sample`;
    }
    if (isPromo) {
        variantLabel += variantLabel ? ` - Promo` : `Promo`;
    }

    return variantLabel.trim();
}